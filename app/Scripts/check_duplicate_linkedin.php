<?php

/**
 * P<PERSON> Script to Check for Duplicate LinkedIn URLs
 * 
 * This script is called by the Python external search to perform early duplicate detection
 * before expensive AI processing. It checks if LinkedIn URLs already exist in the database.
 * 
 * Usage: php check_duplicate_linkedin.php <temp_file_path>
 * 
 * Input JSON format:
 * {
 *   "linkedin_urls": ["url1", "url2", ...],
 *   "plan_id": "plan_id_value"
 * }
 * 
 * Output JSON format:
 * {
 *   "existing_urls": ["url1", "url3", ...],
 *   "total_checked": 100,
 *   "duplicates_found": 20,
 *   "processing_time_ms": 150
 * }
 */

// Start timing
$start_time = microtime(true);

// Check if temp file path is provided
if ($argc < 2) {
    echo json_encode([
        'error' => 'Missing temp file path argument',
        'usage' => 'php check_duplicate_linkedin.php <temp_file_path>'
    ]);
    exit(1);
}

$temp_file_path = $argv[1];

// Check if temp file exists
if (!file_exists($temp_file_path)) {
    echo json_encode([
        'error' => 'Temp file not found',
        'path' => $temp_file_path
    ]);
    exit(1);
}

try {
    // Read and parse input data
    $input_data = json_decode(file_get_contents($temp_file_path), true);
    
    if (!$input_data || !isset($input_data['linkedin_urls'])) {
        echo json_encode([
            'error' => 'Invalid input data format',
            'expected' => 'JSON with linkedin_urls array'
        ]);
        exit(1);
    }
    
    $linkedin_urls = $input_data['linkedin_urls'];
    $plan_id = $input_data['plan_id'] ?? 'unknown';
    
    if (empty($linkedin_urls)) {
        echo json_encode([
            'existing_urls' => [],
            'total_checked' => 0,
            'duplicates_found' => 0,
            'processing_time_ms' => round((microtime(true) - $start_time) * 1000, 2)
        ]);
        exit(0);
    }
    
    // Initialize Laravel application
    require_once __DIR__ . '/../../vendor/autoload.php';
    
    $app = require_once __DIR__ . '/../../bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    
    // Import required models
    use App\Models\People;
    use Illuminate\Support\Facades\Log;
    
    // Log the duplicate check operation
    Log::info("EARLY DUPLICATE DETECTION: Checking LinkedIn URLs", [
        'total_urls' => count($linkedin_urls),
        'plan_id' => $plan_id,
        'sample_urls' => array_slice($linkedin_urls, 0, 3) // Log first 3 URLs as sample
    ]);
    
    // Query database for existing LinkedIn URLs
    // Use chunking for large URL lists to avoid query size limits
    $existing_urls = [];
    $chunk_size = 100; // Process in chunks of 100 URLs
    
    foreach (array_chunk($linkedin_urls, $chunk_size) as $url_chunk) {
        $chunk_existing = People::whereIn('linkedinURL', $url_chunk)
            ->pluck('linkedinURL')
            ->toArray();
        
        $existing_urls = array_merge($existing_urls, $chunk_existing);
    }
    
    // Remove duplicates and ensure array values
    $existing_urls = array_values(array_unique($existing_urls));
    
    $processing_time = round((microtime(true) - $start_time) * 1000, 2);
    
    // Log results
    Log::info("EARLY DUPLICATE DETECTION: Results", [
        'total_checked' => count($linkedin_urls),
        'duplicates_found' => count($existing_urls),
        'duplicate_rate' => count($linkedin_urls) > 0 ? round((count($existing_urls) / count($linkedin_urls)) * 100, 1) . '%' : '0%',
        'processing_time_ms' => $processing_time,
        'plan_id' => $plan_id
    ]);
    
    // Return results as JSON
    echo json_encode([
        'existing_urls' => $existing_urls,
        'total_checked' => count($linkedin_urls),
        'duplicates_found' => count($existing_urls),
        'processing_time_ms' => $processing_time,
        'plan_id' => $plan_id
    ]);
    
} catch (Exception $e) {
    // Log error and return error response
    if (class_exists('Illuminate\Support\Facades\Log')) {
        Log::error("EARLY DUPLICATE DETECTION: Error checking LinkedIn URLs", [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
            'plan_id' => $plan_id ?? 'unknown'
        ]);
    }
    
    echo json_encode([
        'error' => 'Database query failed',
        'message' => $e->getMessage(),
        'processing_time_ms' => round((microtime(true) - $start_time) * 1000, 2)
    ]);
    exit(1);
}
