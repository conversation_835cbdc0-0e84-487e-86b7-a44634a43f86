<?php

/**
 * P<PERSON> Script to Check for Duplicate LinkedIn URLs
 * 
 * This script is called by the Python external search to perform early duplicate detection
 * before expensive AI processing. It checks if LinkedIn URLs already exist in the database.
 * 
 * Usage: php check_duplicate_linkedin.php <temp_file_path>
 * 
 * Input JSON format:
 * {
 *   "linkedin_urls": ["url1", "url2", ...],
 *   "plan_id": "plan_id_value"
 * }
 * 
 * Output JSON format:
 * {
 *   "existing_urls": ["url1", "url3", ...],
 *   "total_checked": 100,
 *   "duplicates_found": 20,
 *   "processing_time_ms": 150
 * }
 */

// Start timing
$start_time = microtime(true);

// Check if temp file path is provided
if ($argc < 2) {
    echo json_encode([
        'error' => 'Missing temp file path argument',
        'usage' => 'php check_duplicate_linkedin.php <temp_file_path>'
    ]);
    exit(1);
}

$temp_file_path = $argv[1];

// Check if temp file exists
if (!file_exists($temp_file_path)) {
    echo json_encode([
        'error' => 'Temp file not found',
        'path' => $temp_file_path
    ]);
    exit(1);
}

try {
    // Read and parse input data
    $input_data = json_decode(file_get_contents($temp_file_path), true);
    
    if (!$input_data || !isset($input_data['linkedin_urls'])) {
        echo json_encode([
            'error' => 'Invalid input data format',
            'expected' => 'JSON with linkedin_urls array'
        ]);
        exit(1);
    }
    
    $linkedin_urls = $input_data['linkedin_urls'];
    $plan_id = $input_data['plan_id'] ?? 'unknown';
    $candidate_profiles = $input_data['candidate_profiles'] ?? []; // New: candidate profile data for name+company matching
    
    if (empty($linkedin_urls)) {
        echo json_encode([
            'existing_urls' => [],
            'total_checked' => 0,
            'duplicates_found' => 0,
            'processing_time_ms' => round((microtime(true) - $start_time) * 1000, 2)
        ]);
        exit(0);
    }
    
    // Initialize Laravel application
    require_once __DIR__ . '/../../vendor/autoload.php';

    $app = require_once __DIR__ . '/../../bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();

    // Import required models (after Laravel bootstrap)
    // Note: In Laravel, we can use the full class names directly
    
    // Log the duplicate check operation
    \Illuminate\Support\Facades\Log::info("ENHANCED EARLY DUPLICATE DETECTION: Checking LinkedIn URLs + Name/Company against both people and pipelines tables", [
        'total_urls' => count($linkedin_urls),
        'total_profiles' => count($candidate_profiles),
        'plan_id' => $plan_id,
        'sample_urls' => array_slice($linkedin_urls, 0, 3), // Log first 3 URLs as sample
        'has_profile_data' => !empty($candidate_profiles)
    ]);

    // STAGE 1: Query people.linkedinURL for existing profiles
    $people_existing_urls = [];
    $chunk_size = 100; // Process in chunks of 100 URLs

    foreach (array_chunk($linkedin_urls, $chunk_size) as $url_chunk) {
        $chunk_existing = \App\Models\People::whereIn('linkedinURL', $url_chunk)
            ->pluck('linkedinURL')
            ->toArray();

        $people_existing_urls = array_merge($people_existing_urls, $chunk_existing);
    }

    // Remove duplicates and ensure array values
    $people_existing_urls = array_values(array_unique($people_existing_urls));

    // STAGE 2: Query pipelines table for plan-specific duplicates
    $pipeline_existing_urls = [];

    if (!empty($plan_id) && $plan_id !== 'unknown') {
        foreach (array_chunk($linkedin_urls, $chunk_size) as $url_chunk) {
            // Query pipelines table for URLs already in this specific plan
            $chunk_pipeline_existing = \App\Models\pipeline::whereIn('linkedinURL', $url_chunk)
                ->where('plan_id', $plan_id)
                ->pluck('linkedinURL')
                ->toArray();

            $pipeline_existing_urls = array_merge($pipeline_existing_urls, $chunk_pipeline_existing);
        }

        // Remove duplicates
        $pipeline_existing_urls = array_values(array_unique($pipeline_existing_urls));
    }

    // STAGE 3: Enhanced Name + Company Matching (if profile data available)
    $name_company_existing_urls = [];
    $name_company_matches = 0;

    if (!empty($candidate_profiles)) {
        \Illuminate\Support\Facades\Log::info("ENHANCED EARLY DUPLICATE DETECTION: Starting name + company matching for " . count($candidate_profiles) . " profiles");

        foreach ($candidate_profiles as $profile) {
            $forename = trim($profile['forename'] ?? '');
            $surname = trim($profile['surname'] ?? '');
            $company_name = trim($profile['company_name'] ?? '');
            $linkedin_url = trim($profile['linkedin_url'] ?? '');

            // Skip if essential data is missing
            if (empty($forename) || empty($surname) || empty($company_name)) {
                continue;
            }

            // Check people table for name + company matches (excluding already found URL matches)
            if (!in_array($linkedin_url, $people_existing_urls)) {
                $existing_by_name = \App\Models\People::where('forename', 'LIKE', $forename)
                    ->where('surname', 'LIKE', $surname)
                    ->where('company_name', 'LIKE', $company_name)
                    ->first();

                if ($existing_by_name && !empty($linkedin_url)) {
                    $name_company_existing_urls[] = $linkedin_url;
                    $name_company_matches++;

                    \Illuminate\Support\Facades\Log::info("ENHANCED EARLY DUPLICATE DETECTION: Found name+company match", [
                        'candidate_name' => "{$forename} {$surname}",
                        'candidate_company' => $company_name,
                        'candidate_url' => $linkedin_url,
                        'existing_person_id' => $existing_by_name->id,
                        'existing_url' => $existing_by_name->linkedinURL
                    ]);
                }
            }
        }

        \Illuminate\Support\Facades\Log::info("ENHANCED EARLY DUPLICATE DETECTION: Name + company matching complete", [
            'profiles_checked' => count($candidate_profiles),
            'name_company_matches' => $name_company_matches
        ]);
    }

    // STAGE 4: Combine all results (union of all sets)
    $all_existing_urls = array_values(array_unique(array_merge(
        $people_existing_urls,
        $pipeline_existing_urls,
        $name_company_existing_urls
    )));

    $processing_time = round((microtime(true) - $start_time) * 1000, 2);

    // Log detailed results
    \Illuminate\Support\Facades\Log::info("ENHANCED EARLY DUPLICATE DETECTION: Detailed Results", [
        'total_checked' => count($linkedin_urls),
        'people_table_url_duplicates' => count($people_existing_urls),
        'pipeline_table_url_duplicates' => count($pipeline_existing_urls),
        'name_company_duplicates' => $name_company_matches,
        'total_duplicates_found' => count($all_existing_urls),
        'duplicate_rate' => count($linkedin_urls) > 0 ? round((count($all_existing_urls) / count($linkedin_urls)) * 100, 1) . '%' : '0%',
        'processing_time_ms' => $processing_time,
        'plan_id' => $plan_id,
        'enhancement_active' => !empty($candidate_profiles)
    ]);
    
    // Return enhanced results as JSON
    echo json_encode([
        'existing_urls' => $all_existing_urls,
        'people_existing_urls' => $people_existing_urls,
        'pipeline_existing_urls' => $pipeline_existing_urls,
        'name_company_existing_urls' => $name_company_existing_urls,
        'total_checked' => count($linkedin_urls),
        'duplicates_found' => count($all_existing_urls),
        'people_duplicates' => count($people_existing_urls),
        'pipeline_duplicates' => count($pipeline_existing_urls),
        'name_company_duplicates' => $name_company_matches,
        'processing_time_ms' => $processing_time,
        'plan_id' => $plan_id,
        'enhancement_active' => !empty($candidate_profiles)
    ]);
    
} catch (Exception $e) {
    // Log error and return error response
    if (class_exists('Illuminate\Support\Facades\Log')) {
        \Illuminate\Support\Facades\Log::error("EARLY DUPLICATE DETECTION: Error checking LinkedIn URLs", [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
            'plan_id' => $plan_id ?? 'unknown'
        ]);
    }
    
    echo json_encode([
        'error' => 'Database query failed',
        'message' => $e->getMessage(),
        'processing_time_ms' => round((microtime(true) - $start_time) * 1000, 2)
    ]);
    exit(1);
}
