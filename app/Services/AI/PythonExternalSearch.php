<?php

namespace App\Services\AI;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\People;
use App\Models\Company;
use App\Models\Skills;
use App\Models\CareerHistories;
use App\Models\pipeline;

class PythonExternalSearch
{
    /**
     * Python executable path
     */
    protected $pythonPath;

    /**
     * Python script path
     */
    protected $scriptPath;

    /**
     * Constructor
     */
    public function __construct()
    {
        // Get these from config or use defaults
        $venvPython = base_path('venv/bin/python');
        if (file_exists($venvPython)) {
            $this->pythonPath = $venvPython;
        } else {
            $this->pythonPath = config('ai.python.binary_path', 'python');
        }
        $this->scriptPath = base_path('scripts/python/exa_search_standalone.py');
    }

    /**
     * Main method to execute external candidate search using Python script
     *
     * EARLY DUPLICATE DETECTION: The Python script now performs early duplicate detection
     * by checking LinkedIn URLs against the database before AI processing, reducing costs by ~20%.
     *
     * @param array $planData The succession plan data
     * @param object $user The current user
     * @return array List of candidate profiles found
     */
    public function searchExternalCandidates($planData, $user)
    {
        try {

            // Use a dedicated shared directory instead of current working directory
            $searchDir = base_path('shared'); // Shared directory accessible by both PHP and Python with correct permissions
            
            // Create the shared directory if it doesn't exist
            if (!is_dir($searchDir)) {
                mkdir($searchDir, 0755, true);
            }
            
            // Make sure we have a unique filename prefix for our files
            $timestamp = time();

            // Prepare input and output file paths in the shared directory
            $inputFile = $searchDir . '/search_params_' . $timestamp . '.json';
            $outputFile = $searchDir . '/search_results_' . $timestamp . '.json';

            // Write plan data to input file
            file_put_contents($inputFile, json_encode($planData, JSON_PRETTY_PRINT));

            // Convert to absolute paths if they aren't already
            $inputFileAbs = realpath($inputFile);
            $outputFileAbs = $outputFile; // Cannot use realpath on a file that doesn't exist yet
            
            if (!$inputFileAbs) {
                Log::error("PYTHON EXTERNAL SEARCH: Failed to get absolute path for input file: {$inputFile}");
                $inputFileAbs = $inputFile; // Fallback to the original path
            }
            
            
            // Construct command with proper escaping and absolute paths
            $command = escapeshellcmd($this->pythonPath) . ' ' .
                      escapeshellarg($this->scriptPath) . ' ' .
                      '--plan-file ' . escapeshellarg($inputFileAbs) . ' ' .
                      '--output ' . escapeshellarg($outputFileAbs) . ' ' .
                      '--num-results 100';

            
            // Execute the Python script with enhanced logging
            $output = [];
            $returnCode = 0;
            $scriptStartTime = microtime(true);

            Log::info("PYTHON SCRIPT EXECUTION: Starting enhanced external search script", [
                'command' => $command,
                'plan_id' => $planData['plan_id'] ?? 'unknown',
                'input_file' => $inputFileAbs,
                'output_file' => $outputFileAbs
            ]);

            exec($command, $output, $returnCode);

            $scriptExecutionTime = round(microtime(true) - $scriptStartTime, 2);

            // Log Python script output for debugging early duplicate detection
            $outputString = implode("\n", $output);
            Log::info("PYTHON SCRIPT EXECUTION: Script completed", [
                'return_code' => $returnCode,
                'execution_time' => $scriptExecutionTime . ' seconds',
                'output_length' => strlen($outputString),
                'contains_early_duplicate_logs' => strpos($outputString, 'ENHANCED EARLY DUPLICATE DETECTION') !== false,
                'contains_cost_savings_logs' => strpos($outputString, 'COST SAVINGS') !== false
            ]);

            // Check for execution errors
            if ($returnCode !== 0) {
                Log::error("PYTHON EXTERNAL SEARCH: Script execution failed with code {$returnCode}", [
                    'command' => $command,
                    'output' => $outputString,
                    'execution_time' => $scriptExecutionTime . ' seconds',
                    'working_directory' => getcwd(),
                    'script_path' => $this->scriptPath,
                    'input_file' => $inputFileAbs,
                    'output_file' => $outputFileAbs
                ]);
                return [];
            }

            // Check if output file exists
            if (!file_exists($outputFile)) {
                Log::error("PYTHON EXTERNAL SEARCH: Output file {$outputFile} not found", [
                    'command' => $command,
                    'output' => implode("\n", $output),
                    'working_directory' => getcwd(),
                    'temp_dir_contents' => implode("\n", array_filter(scandir($searchDir), function($item) { return !in_array($item, ['.', '..']); })),
                    'script_path' => $this->scriptPath,
                    'input_file' => $inputFileAbs,
                    'output_file' => $outputFileAbs
                ]);
                
                // Check if output was written to a different location
                $tempOutputFile = sys_get_temp_dir() . '/linkedin_profiles.json';
                $workingDirOutput = getcwd() . '/search_results_' . $timestamp . '.json';
                
                if (file_exists($tempOutputFile)) {
                    copy($tempOutputFile, $outputFile);
                } else if (file_exists($workingDirOutput)) {
                    copy($workingDirOutput, $outputFile);
                }
                
                // Check again after potential copy
                if (!file_exists($outputFile)) {
                    return [];
                }
            }

            // Read and parse results with enhanced analysis
            $resultsJson = file_get_contents($outputFile);
            $results = json_decode($resultsJson, true);

            // Analyze Python script results for early duplicate detection effectiveness
            $this->analyzeEarlyDuplicateDetection($results, $planData);

            // Clean up temporary files
            @unlink($inputFile);
            @unlink($outputFile);

            // Process the results and save to database
            $candidates = $this->processResults($results, $planData, $user);
            
            return $candidates;

        } catch (\Exception $e) {
            Log::error("PYTHON EXTERNAL SEARCH: Exception in searchExternalCandidates", [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return empty array to avoid breaking the job
            return [];
        }
    }

    /**
     * Process results from Python script and save to database
     *
     * @param array $results The Python script results
     * @param array $planData The plan data
     * @param object $user The current user
     * @return array Processed candidate list
     */
    protected function processResults($results, $planData, $user)
    {
        try {
            // Extract profiles from results
            $profiles = $results['profiles'] ?? [];
            if (empty($profiles)) {
                return [];
            }
            
            $candidates = [];
            $duplicates = 0;

            foreach ($profiles as $profile) {
                try {
                    // Validate required data
                    if (empty($profile['people_data']) || empty($profile['pipeline_data'])) {
                        Log::warning("Missing required data in profile");
                        continue;
                    }

                    // Extract people and pipeline data
                    $peopleData = $profile['people_data'];
                    $pipelineData = $profile['pipeline_data'];

                    // ENHANCED EARLY DUPLICATE DETECTION:
                    // The Python script should have already filtered out duplicates before AI processing.
                    // If we reach this point, these should be new candidates that need to be created.

                    $linkedinURL = $peopleData['linkedinURL'] ?? '';
                    $forename = $peopleData['forename'] ?? '';
                    $surname = $peopleData['surname'] ?? '';
                    $company_name = $peopleData['company_name'] ?? '';

                    // FALLBACK DUPLICATE CHECK: Only check for edge cases that early detection might have missed
                    $existingByURL = null;
                    if (!empty($linkedinURL)) {
                        $existingByURL = People::where('linkedinURL', $linkedinURL)->first();
                        if ($existingByURL) {
                            Log::warning("FALLBACK DUPLICATE DETECTION: Found duplicate that early detection missed", [
                                'linkedin_url' => $linkedinURL,
                                'name' => "{$forename} {$surname}",
                                'existing_person_id' => $existingByURL->id
                            ]);
                        }
                    }

                    // Handle the person record
                    $person = null;

                    if ($existingByURL) {
                        // Use existing person (this should be rare if early detection is working)
                        $person = $existingByURL;
                        $duplicates++;
                        Log::info("FALLBACK DUPLICATE: Using existing person record", [
                            'name' => "{$forename} {$surname}",
                            'company' => $company_name,
                            'person_id' => $person->id
                        ]);
                    } else {
                        // Prepare and create new people record
                        $personData = $peopleData;
                        $personData['user_id'] = $user->id;
                        $personData['status'] = 'Submitted';
                        $personData['readiness'] = 'Not Ready';

                        
                        // Remove fields that are not in the People model schema
                        if (isset($personData['contact_number'])) {
                            unset($personData['contact_number']);
                        }

                        if (isset($personData['email_address'])) {
                            unset($personData['email_address']);
                        }
                        
                        // Remove location field which is not in the People model schema
                        if (isset($personData['location'])) {
                            unset($personData['location']);
                        }
                        
                        // Handle field mapping for first_name/last_name to forename/surname if needed
                        if (isset($personData['first_name']) && !isset($personData['forename'])) {
                            $personData['forename'] = $personData['first_name'];
                            unset($personData['first_name']);
                        }
                        
                        if (isset($personData['last_name']) && !isset($personData['surname'])) {
                            $personData['surname'] = $personData['last_name'];
                            unset($personData['last_name']);
                        }

                        // Get or create company
                        $company = Company::where('name', 'LIKE', $company_name)->first();
                        
                        if (!$company && !empty($company_name)) {
                            try {
                                $company = Company::create([
                                    'name' => $company_name,
                                    'status' => 'Active',
                                    'website' => null,
                                    'phone' => null,
                                    'logo' => null,
                                    'address' => null,
                                    'location_id' => 1, // Default location ID
                                    'company_size' => 'Unknown',
                                    'industry' => 'Unknown',
                                    'sector' => 'Unknown'
                                ]);
                                $personData['company_id'] = $company->id;
                                Log::info("Created new company: {$company_name}");
                            } catch (\Exception $e) {
                                Log::warning("Failed to create company: {$e->getMessage()}");
                                // Try to use a default company
                                $defaultCompany = Company::first();
                                if ($defaultCompany) {
                                    $personData['company_id'] = $defaultCompany->id;
                                    Log::info("Using default company instead");
                                }
                            }
                        } else if ($company) {
                            $personData['company_id'] = $company->id;
                        }

                        // Create person record
                        $person = People::create($personData);
                        Log::info("DATABASE INSERT: Created new person record in People table", [
                            'person_id' => $person->id,
                            'name' => "{$forename} {$surname}",
                            'company' => $company_name,
                            'role' => $personData['latest_role'] ?? 'Unknown',
                            'linkedin' => $personData['linkedinURL'] ?? 'None'
                        ]);
                    }

                    // Check if person is already in pipeline for this plan
                    $existingPipeline = pipeline::where('people_id', $person->id)
                        ->where('plan_id', $planData['plan_id'])
                        ->first();

                    if ($existingPipeline) {
                        Log::info("PIPELINE DUPLICATE DETECTION (pipelines.people_id + plan_id): Person already in pipeline for this plan", [
                            'name' => "{$person->forename} {$person->surname}",
                            'people_id' => $person->id,
                            'plan_id' => $planData['plan_id'],
                            'existing_pipeline_id' => $existingPipeline->id
                        ]);
                    } else {
                        // Prepare pipeline data
                        $pipelineData['plan_id'] = $planData['plan_id'];
                        $pipelineData['user_id'] = $user->id;
                        $pipelineData['people_id'] = $person->id;
                        $pipelineData['people_type'] = 'External-AI';
                        $pipelineData['company_id'] = $person->company_id;
                        
                        // Make sure summary is copied from person record if not already in pipeline data
                        if (empty($pipelineData['summary']) && !empty($person->summary)) {
                            $pipelineData['summary'] = $person->summary;
                            Log::info("Copied summary from person record to pipeline");
                        }
                        
                        // Make sure all score fields are properly set
                        $pipelineData['role_match'] = $profile['pipeline_data']['role_match'] ?? 0;
                        $pipelineData['skills_match'] = $profile['pipeline_data']['skills_match'] ?? 0;
                        $pipelineData['location_match'] = $profile['pipeline_data']['location_match'] ?? 0;
                        $pipelineData['gender_match'] = $profile['pipeline_data']['gender_match'] ?? 0;
                        $pipelineData['tenure_match'] = $profile['pipeline_data']['tenure_match'] ?? 0;
                        $pipelineData['education_match'] = $profile['pipeline_data']['education_match'] ?? 0;
                        $pipelineData['total_score'] = $profile['pipeline_data']['total_score'] ?? 0;

                        // Role matching removed as requested
                        // We're accepting all profiles regardless of role match
                        $candidateRole = trim($pipelineData['latest_role']);
                        
                        // Retain original role_match score from AI without rescoring
                        // Don't modify total_score based on role_match adjustment
                        
                        // No longer skipping candidates with zero role_match

                        // Add company_id to pipeline data
                        $pipelineData['company_id'] = $person->company_id;

                        // Set contact info in pipeline data
                        // If contact info isn't provided, default to empty string
                        if (!isset($pipelineData['contact_number'])) {
                            $pipelineData['contact_number'] = '';
                        }

                        if (!isset($pipelineData['email_address'])) {
                            $pipelineData['email_address'] = '';
                        }

                        // Create career history entries
                        $this->createCareerHistoryEntries($person->id, $person->career_history, $person->company_id);

                        // Create skill entries
                        $this->createSkillEntries($person->id, $person->skills);

                        // Create pipeline record
                        $pipeline = pipeline::create($pipelineData);
                        
                        // Log detailed pipeline information
                        Log::info("DATABASE INSERT: Created new pipeline record", [
                            'pipeline_id' => $pipeline->id,
                            'person_id' => $person->id,
                            'plan_id' => $planData['plan_id'],
                            'name' => "{$person->forename} {$person->surname}",
                            'role' => $pipelineData['latest_role'],
                            'role_match' => $pipelineData['role_match'],
                            'skills_match' => $pipelineData['skills_match'],
                            'total_score' => $pipelineData['total_score']
                        ]);

                        // Add to candidates list for return
                        $candidates[] = [
                            'name' => $person->forename . ' ' . $person->surname,
                            'role' => $pipelineData['latest_role'],
                            'company' => $pipelineData['company_name'],
                            'url' => $pipelineData['linkedinURL'],
                            'score' => $pipelineData['total_score'] ?? 0,
                            'reasoning' => $profile['match_reasoning'] ?? ''
                        ];

                        Log::info("Added candidate to pipeline: {$person->forename} {$person->surname}");
                    }
                } catch (\Exception $e) {
                    Log::error("Error processing individual profile: {$e->getMessage()}");
                    continue;
                }
            }

            // Create notification
            $notificationData = [
                'user_id' => $user->id,
                'created_at' => now(),
            ];
            DB::table('job_queues_notification')->insert($notificationData);

            // Create a detailed search completion log with enhanced duplicate detection metrics
            $successCount = count($candidates);
            $executionTime = round(microtime(true) - LARAVEL_START, 2);
            $totalProfilesFromPython = count($profiles);

            // Monitor early duplicate detection effectiveness
            $this->monitorEarlyDuplicateDetection($duplicates, $planData);

            // Log the enhanced performance metrics
            Log::info("ENHANCED EXTERNAL SEARCH: Performance Summary", [
                'profiles_from_python_script' => $totalProfilesFromPython,
                'new_candidates_created' => $successCount,
                'fallback_duplicates_found' => $duplicates,
                'early_duplicate_detection_effectiveness' => $duplicates === 0 ? 'WORKING PERFECTLY' : 'NEEDS ATTENTION',
                'plan_id' => $planData['plan_id'],
                'execution_time' => $executionTime . ' seconds'
            ]);

            // Add a prominent search completion log entry
            Log::channel('daily')->info("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            Log::channel('daily')->info("🔍 ENHANCED EXTERNAL SEARCH COMPLETED: Plan #{$planData['plan_id']} - {$planData['plan_name']}", [
                'execution_time' => $executionTime . ' seconds',
                'candidates_found' => $successCount,
                'fallback_duplicates_found' => $duplicates,
                'total_profiles_processed' => $totalProfilesFromPython,
                'early_detection_status' => $duplicates === 0 ? '✅ WORKING' : '⚠️ NEEDS ATTENTION',
                'target_roles' => $planData['target_roles'] ?? [],
                'search_query' => $planData['search_query'] ?? 'None',
                'user_id' => $user->id,
                'user_email' => $user->email,
                'timestamp' => now()->toDateTimeString()
            ]);
            Log::channel('daily')->info("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            
            return $candidates;

        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            $executionTime = round(microtime(true) - LARAVEL_START, 2);
            
            // Log detailed error information
            Log::error("Error processing Python script results: {$errorMessage}", [
                'trace' => $e->getTraceAsString()
            ]);
            
            // Add a prominent error log entry
            Log::channel('daily')->error("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            Log::channel('daily')->error("❌ EXTERNAL SEARCH FAILED: Plan #{$planData['plan_id']} - {$planData['plan_name']}", [
                'execution_time' => $executionTime . ' seconds',
                'error' => $errorMessage,
                'target_roles' => $planData['target_roles'] ?? [],
                'search_query' => $planData['search_query'] ?? 'None',
                'user_id' => $user->id,
                'user_email' => $user->email,
                'timestamp' => now()->toDateTimeString()
            ]);
            Log::channel('daily')->error("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            
            return [];
        }
    }

    /**
     * Analyze early duplicate detection effectiveness from Python script results
     *
     * @param array $results The Python script results
     * @param array $planData The plan data
     */
    protected function analyzeEarlyDuplicateDetection($results, $planData)
    {
        try {
            // Extract metrics from Python script results
            $profiles = $results['profiles'] ?? [];
            $metrics = $results['metrics'] ?? [];
            $duplicateStats = $results['duplicate_detection'] ?? [];

            $profileCount = count($profiles);
            $originalSearchResults = $metrics['original_search_results'] ?? 'unknown';
            $duplicatesFiltered = $metrics['duplicates_filtered'] ?? 0;
            $earlyDetectionTime = $metrics['early_detection_time_ms'] ?? 0;

            Log::info("EARLY DUPLICATE DETECTION ANALYSIS: Python script results", [
                'plan_id' => $planData['plan_id'] ?? 'unknown',
                'original_exa_results' => $originalSearchResults,
                'duplicates_filtered_early' => $duplicatesFiltered,
                'profiles_sent_to_ai' => $originalSearchResults !== 'unknown' ? ($originalSearchResults - $duplicatesFiltered) : 'unknown',
                'final_profiles_from_ai' => $profileCount,
                'early_detection_time_ms' => $earlyDetectionTime,
                'early_detection_effectiveness' => $duplicatesFiltered > 0 ? 'WORKING' : 'NO_DUPLICATES_FOUND'
            ]);

            // Check for enhanced duplicate breakdown
            if (!empty($duplicateStats)) {
                $enhancementActive = $duplicateStats['enhancement_active'] ?? false;

                Log::info("ENHANCED EARLY DUPLICATE DETECTION: Comprehensive breakdown from Python script", [
                    'people_table_duplicates' => $duplicateStats['people_duplicates'] ?? 0,
                    'pipeline_table_duplicates' => $duplicateStats['pipeline_duplicates'] ?? 0,
                    'name_company_duplicates' => $duplicateStats['name_company_duplicates'] ?? 0,
                    'total_duplicates_filtered' => $duplicateStats['total_duplicates'] ?? 0,
                    'database_query_time_ms' => $duplicateStats['processing_time_ms'] ?? 0,
                    'enhancement_status' => $enhancementActive ? 'ACTIVE (URL + Name/Company)' : 'BASIC (URL only)',
                    'plan_id' => $planData['plan_id'] ?? 'unknown'
                ]);

                // Log enhancement effectiveness
                $nameCompanyDuplicates = $duplicateStats['name_company_duplicates'] ?? 0;
                if ($nameCompanyDuplicates > 0) {
                    Log::info("ENHANCED DUPLICATE DETECTION SUCCESS: Name+Company matching found additional duplicates", [
                        'additional_duplicates_caught' => $nameCompanyDuplicates,
                        'enhancement_value' => 'Prevented ' . $nameCompanyDuplicates . ' additional profiles from AI processing',
                        'plan_id' => $planData['plan_id'] ?? 'unknown'
                    ]);
                }
            }

            // Calculate cost savings if duplicates were filtered
            if ($duplicatesFiltered > 0) {
                $estimatedCostSavings = $duplicatesFiltered * 0.02; // $0.02 per AI profile processing
                $estimatedTimeSavings = $duplicatesFiltered * 3; // ~3 seconds per profile

                Log::info("EARLY DUPLICATE DETECTION: Cost savings achieved", [
                    'duplicates_filtered' => $duplicatesFiltered,
                    'estimated_cost_savings' => '$' . number_format($estimatedCostSavings, 2),
                    'estimated_time_savings' => $estimatedTimeSavings . ' seconds',
                    'efficiency_gain' => $originalSearchResults > 0 ? round(($duplicatesFiltered / $originalSearchResults) * 100, 1) . '%' : 'unknown',
                    'plan_id' => $planData['plan_id'] ?? 'unknown'
                ]);
            }

        } catch (\Exception $e) {
            Log::warning("Failed to analyze early duplicate detection results", [
                'error' => $e->getMessage(),
                'plan_id' => $planData['plan_id'] ?? 'unknown'
            ]);
        }
    }

    /**
     * Monitor and alert on early duplicate detection effectiveness
     *
     * @param int $duplicatesFound Number of fallback duplicates found
     * @param array $planData The plan data
     */
    protected function monitorEarlyDuplicateDetection($duplicatesFound, $planData)
    {
        // If we find duplicates in PHP, it means early detection failed
        if ($duplicatesFound > 0) {
            // Log warning for immediate attention
            Log::warning("EARLY DUPLICATE DETECTION FAILURE: Found {$duplicatesFound} duplicates that should have been filtered by Python script", [
                'plan_id' => $planData['plan_id'] ?? 'unknown',
                'plan_name' => $planData['plan_name'] ?? 'Unknown Plan',
                'fallback_duplicates' => $duplicatesFound,
                'impact' => 'Wasted AI processing costs and time',
                'action_required' => 'Check Python script early duplicate detection implementation'
            ]);

            // Calculate wasted resources
            $wastedCost = $duplicatesFound * 0.02; // $0.02 per AI profile processing
            $wastedTime = $duplicatesFound * 3; // ~3 seconds per profile

            Log::warning("RESOURCE WASTE ALERT: Early duplicate detection failure caused resource waste", [
                'wasted_cost_usd' => number_format($wastedCost, 2),
                'wasted_time_seconds' => $wastedTime,
                'duplicates_processed_unnecessarily' => $duplicatesFound,
                'plan_id' => $planData['plan_id'] ?? 'unknown'
            ]);

            // Create a daily summary log for tracking
            Log::channel('daily')->warning("🚨 EARLY DUPLICATE DETECTION FAILURE", [
                'date' => now()->toDateString(),
                'plan_id' => $planData['plan_id'] ?? 'unknown',
                'fallback_duplicates' => $duplicatesFound,
                'wasted_cost' => '$' . number_format($wastedCost, 2),
                'wasted_time' => $wastedTime . 's',
                'recommendation' => 'Investigate Python script early duplicate detection'
            ]);
        } else {
            // Log success for monitoring
            Log::info("EARLY DUPLICATE DETECTION SUCCESS: No fallback duplicates found - early detection working correctly", [
                'plan_id' => $planData['plan_id'] ?? 'unknown',
                'status' => 'WORKING_CORRECTLY'
            ]);
        }
    }

    /**
     * Parse career history text and create entries
     *
     * @param int $peopleId The person's ID
     * @param string $careerHistoryText The raw career history text
     * @param int $currentCompanyId The ID of the current company
     */
    protected function createCareerHistoryEntries($peopleId, $careerHistoryText, $currentCompanyId)
    {
        if (empty($careerHistoryText)) {
            Log::info("No career history to parse for people_id: {$peopleId}");
            return;
        }

        // Check if entries already exist
        $existingEntries = CareerHistories::where('people_id', $peopleId)->count();
        if ($existingEntries > 0) {
            Log::info("Career history entries already exist for people_id: {$peopleId}");
            return;
        }

        // Log the full career history
        Log::info("DATABASE: Processing career history for people_id: {$peopleId}", [
            'career_history_text' => $careerHistoryText
        ]);

        // Split into individual positions
        $positions = explode("\n", $careerHistoryText);
        $processedPositions = [];
        $totalTenure = 0;
        $companies = [];

        foreach ($positions as $position) {
            if (empty(trim($position))) {
                continue;
            }

            // Parse the position string
            $dateParts = [];
            if (preg_match('/^(.*?)\s*\|\s*(.*?):\s*(.*)$/i', $position, $dateParts)) {
                $startDateRaw = trim($dateParts[1]);
                $endDateRaw = trim($dateParts[2]);
                $roleAndCompany = trim($dateParts[3]);

                // Extract role and company
                $companyParts = [];
                $role = $roleAndCompany;
                $companyName = '';

                if (preg_match('/(.*)\s+at\s+(.*)$/i', $roleAndCompany, $companyParts)) {
                    $role = trim($companyParts[1]);
                    $companyName = trim($companyParts[2]);

                    // Remove location if present
                    if (strpos($companyName, ',') !== false) {
                        $companyName = trim(explode(',', $companyName)[0]);
                    }
                }

                // Find or create company
                $company = Company::where('name', 'LIKE', $companyName)->first();
                if (!$company && !empty($companyName)) {
                    try {
                        $company = Company::create([
                            'name' => $companyName,
                            'status' => 'Active',
                            'website' => null,
                            'phone' => null,
                            'logo' => null,
                            'address' => null,
                            'location_id' => 1,
                            'company_size' => 'Unknown',
                            'industry' => 'Unknown',
                            'sector' => 'Unknown'
                        ]);
                        
                        Log::info("DATABASE INSERT: Created new company for career history", [
                            'company_id' => $company->id,
                            'company_name' => $companyName
                        ]);
                    } catch (\Exception $e) {
                        // Use current company as fallback
                        $company = Company::find($currentCompanyId);
                        Log::warning("Failed to create company, using fallback", [
                            'company_name' => $companyName,
                            'fallback_id' => $currentCompanyId,
                            'error' => $e->getMessage()
                        ]);
                    }
                }

                // Skip if no company
                if (!$company) {
                    Log::warning("Skipping career history entry - no company found", [
                        'company_name' => $companyName
                    ]);
                    continue;
                }

                // Skip current position
                if (strtolower($endDateRaw) === 'present' && $company->id === $currentCompanyId) {
                    Log::info("Skipping current position (already stored in people record)", [
                        'role' => $role,
                        'company' => $companyName
                    ]);
                    continue;
                }

                // Parse dates
                $startDate = $this->parseCareerDate($startDateRaw);
                $endDate = strtolower($endDateRaw) === 'present' ? now() : $this->parseCareerDate($endDateRaw);

                // Calculate tenure
                $tenure = 0;
                if ($startDate && $endDate) {
                    $startDateTime = new \DateTime($startDate);
                    $endDateTime = new \DateTime($endDate);
                    $interval = $startDateTime->diff($endDateTime);
                    $tenure = $interval->y + ($interval->m / 12);
                }

                // Create career history entry
                $entry = CareerHistories::create([
                    'people_id' => $peopleId,
                    'role' => $role,
                    'past_company_id' => $company->id,
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'tenure' => round($tenure, 1)
                ]);

                $formattedTenure = round($tenure, 1);
                $totalTenure += $formattedTenure;
                $companies[] = $companyName;
                
                $processedPositions[] = [
                    'id' => $entry->id,
                    'role' => $role,
                    'company' => $companyName,
                    'tenure' => $formattedTenure,
                    'period' => "{$startDateRaw} to {$endDateRaw}"
                ];
            }
        }
        
        // Log summary of career history
        if (count($processedPositions) > 0) {
            Log::info("DATABASE INSERT: Added " . count($processedPositions) . " career history entries for people_id: {$peopleId}", [
                'total_tenure' => $totalTenure,
                'companies' => array_unique($companies),
                'positions' => $processedPositions
            ]);
        }
    }

    /**
     * Parse skills text and create entries
     *
     * @param int $peopleId The person's ID
     * @param string $skillsText The raw skills text
     */
    protected function createSkillEntries($peopleId, $skillsText)
    {
        if (empty($skillsText)) {
            Log::info("No skills to parse for people_id: {$peopleId}");
            return;
        }

        // Check if entries already exist
        $existingEntries = Skills::where('people_id', $peopleId)->count();
        if ($existingEntries > 0) {
            Log::info("Skill entries already exist for people_id: {$peopleId}");
            return;
        }

        // Split into individual skills
        $skills = array_map('trim', explode(',', $skillsText));
        
        // Log the skills being processed
        Log::info("DATABASE: Processing " . count($skills) . " skills for people_id: {$peopleId}", [
            'skills_text' => $skillsText
        ]);

        $skillsByType = [
            'Technical' => [],
            'Leadership' => [],
            'Communication' => [],
            'Financial' => [],
            'Other' => []
        ];

        foreach ($skills as $skill) {
            if (empty($skill)) {
                continue;
            }

            // Determine skill type
            $skillType = $this->determineSkillType($skill);
            
            // Add to type collection for summary
            $skillsByType[$skillType][] = $skill;

            // Create skill entry
            $skillEntry = Skills::create([
                'people_id' => $peopleId,
                'skill_name' => $skill,
                'skill_type' => $skillType
            ]);
        }
        
        // Log a summary of skills by type
        foreach ($skillsByType as $type => $typeSkills) {
            if (count($typeSkills) > 0) {
                Log::info("DATABASE INSERT: Added " . count($typeSkills) . " {$type} skills for people_id: {$peopleId}", [
                    'skills' => implode(', ', $typeSkills)
                ]);
            }
        }
    }

    /**
     * Parse a date string from career history format
     *
     * @param string $dateString The date string to parse
     * @return string|null The parsed date in Y-m-d format
     */
    protected function parseCareerDate($dateString)
    {
        try {
            // Handle 'Present' case
            if (strtolower($dateString) === 'present') {
                return date('Y-m-d');
            }

            // Parse "MMM-YYYY" format
            if (preg_match('/^([A-Za-z]{3,})-(\d{4})$/', $dateString, $matches)) {
                $month = $matches[1];
                $year = $matches[2];

                // Convert month name to number
                $dateObj = \DateTime::createFromFormat('M Y', substr($month, 0, 3) . ' ' . $year);
                if ($dateObj) {
                    return $dateObj->format('Y-m-d');
                }
            }

            // Try generic date parsing
            $date = \DateTime::createFromFormat('Y-m-d', $dateString);
            if (!$date) {
                $timestamp = strtotime($dateString);
                if ($timestamp === false) {
                    return null;
                }
                return date('Y-m-d', $timestamp);
            }

            return $date->format('Y-m-d');
        } catch (\Exception $e) {
            Log::warning("Failed to parse date: {$dateString}");
            return null;
        }
    }

    /**
     * Determine skill type based on keywords
     *
     * @param string $skill The skill name
     * @return string The determined skill type
     */
    protected function determineSkillType($skill)
    {
        $skill = strtolower($skill);

        // Technical skills
        $technicalKeywords = [
            'programming', 'coding', 'software', 'database', 'sql', 'python', 'java', 'c++',
            'javascript', 'react', 'angular', 'vue', 'node', 'aws', 'azure', 'cloud',
            'devops', 'infrastructure', 'security', 'network', 'system', 'architecture',
            'engineering', 'technical', 'development', 'automation', 'testing',
            'drilling', 'rig', 'offshore', 'onshore', 'oil', 'gas', 'petroleum', 'platform',
            'mechanical', 'electrical', 'operations', 'maintenance', 'safety', 'hsse'
        ];

        // Leadership skills
        $leadershipKeywords = [
            'leadership', 'management', 'strategic', 'executive', 'director', 'c-level',
            'board', 'governance', 'vision', 'strategy', 'planning', 'transformation',
            'change management', 'organizational', 'team lead', 'mentoring', 'coaching',
            'supervision', 'decision-making', 'delegation', 'influence'
        ];

        // Communication skills
        $communicationKeywords = [
            'communication', 'presentation', 'negotiation', 'public speaking', 'writing',
            'reporting', 'stakeholder', 'client', 'relationship', 'facilitation',
            'training', 'teaching', 'persuasion', 'influence', 'networking', 'collaboration'
        ];

        // Financial skills
        $financialKeywords = [
            'financial', 'finance', 'accounting', 'budget', 'forecasting', 'investment',
            'revenue', 'profit', 'loss', 'balance sheet', 'p&l', 'tax', 'audit',
            'cost', 'pricing', 'valuation', 'funding', 'capital', 'banking', 'treasury'
        ];

        // Check skill against keyword lists
        foreach ($technicalKeywords as $keyword) {
            if (strpos($skill, $keyword) !== false) {
                return "Technical";
            }
        }

        foreach ($leadershipKeywords as $keyword) {
            if (strpos($skill, $keyword) !== false) {
                return "Leadership";
            }
        }

        foreach ($communicationKeywords as $keyword) {
            if (strpos($skill, $keyword) !== false) {
                return "Communication";
            }
        }

        foreach ($financialKeywords as $keyword) {
            if (strpos($skill, $keyword) !== false) {
                return "Financial";
            }
        }

        // Default
        return "Other";
    }
}