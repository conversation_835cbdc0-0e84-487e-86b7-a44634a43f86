#!/usr/bin/env python3
"""
Exa AI External Search - Standalone Script

A standalone Python script that implements the same external search functionality
as the Laravel application, but in a self-contained Python script.
"""

import os
import sys
import json
import time
import argparse
import logging
import traceback
import asyncio
from datetime import datetime
from exa_py import Exa
import instructor
from openai import OpenAI
from openai import AsyncOpenAI
import aiohttp
from pydantic import BaseModel, Field, field_validator
from typing import List, Optional, Dict, Any, Union, Tuple
import re
from datetime import datetime

# Configure logging
import os

# Determine log directory (storage/logs)
script_dir = os.path.dirname(os.path.abspath(__file__))
base_dir = os.path.abspath(os.path.join(script_dir, '..', '..'))
log_dir = os.path.join(base_dir, 'storage', 'logs')

# Ensure log directory exists
os.makedirs(log_dir, exist_ok=True)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(log_dir, "exa_search.log"))
    ]
)
logger = logging.getLogger(__name__)

# Function to load API keys from .env file
def load_env_vars():
    """Load environment variables from .env file if it exists"""
    # Look for .env file in project root directory
    env_path = os.path.join(base_dir, '.env')
    
    if os.path.exists(env_path):
        logger.info(f"Loading environment variables from {env_path}...")
        try:
            with open(env_path, 'r') as file:
                for line in file:
                    line = line.strip()
                    # Skip comments and empty lines
                    if not line or line.startswith('#'):
                        continue
                    # Parse key-value pairs
                    if '=' in line:
                        key, value = line.split('=', 1)
                        # Strip quotes if present
                        value = value.strip('"\'')
                        os.environ[key] = value
            logger.info("Successfully loaded environment variables")
        except Exception as e:
            logger.error(f"Error loading .env file: {str(e)}")
    else:
        logger.info(f"No .env file found at {env_path}, using existing environment variables")

# Define Pydantic models for structured output
class PeopleData(BaseModel):
    # Fields matching the People table in the Laravel implementation
    forename: str = Field(description="First name", min_length=1, max_length=50)
    surname: str = Field(description="Last name", min_length=1, max_length=50)
    gender: Optional[str] = Field(default=None, description="ONLY 'Male' or 'Female' (use best guess based on first and last name)")
    diverse: Optional[str] = Field(default=None, description="ONLY one of: Irish/Italian/Asian/German/English/Indian/French/Spanish/Other (infer likely ethnicity based on full name)")
    country: Optional[str] = Field(default=None, description="Country of residence - use full country name, not abbreviations")
    city: Optional[str] = Field(default=None, description="City of residence - use proper capitalization")
    # Location is handled by country and city fields only (no location field in people table)
    linkedinURL: str = Field(description="LinkedIn profile URL - must start with 'https://www.linkedin.com/'")
    latest_role: str = Field(description="ONLY the most recent/current job title (extract just the specific role title without the company name)", min_length=2, max_length=100)
    company_name: Optional[str] = Field(default="Not Specified", description="Current company name - use official company name", min_length=1, max_length=100)
    company_id: Optional[int] = Field(default=0, description="ID of the company in the database - will be set by the system")
    start_date: Optional[str] = Field(default=None, description="Start date of current role - STRICTLY use YYYY-MM-DD format")
    end_date: Optional[str] = Field(default=None, description="null if still employed, YYYY-MM-DD if left role")
    tenure: Optional[int] = Field(default=0, description="Years in current role - must be a positive integer or zero", ge=0, le=50)
    function: Optional[str] = Field(default=None, description="Function area (e.g., Operations, Finance, Marketing, etc.)")
    division: Optional[str] = Field(default=None, description="Division within company - use actual division name if available")
    seniority: Optional[str] = Field(default=None, description="ONLY one of: Head/Manager/Director/VP/C-Suite/Other")
    exco: Optional[str] = Field(default="Non Exco", description="ONLY 'Exco' or 'Non Exco' for executive committee status")
    career_history: Optional[str] = Field(default=None, description="EXTRACT THE FULL CAREER HISTORY, INCLUDING ALL POSITIONS. Format as chronological list with dates, titles, companies, and locations")
    educational_history: Optional[str] = Field(default=None, description="Format as 'Institution, Field of Study - Degree Type · Graduation Year'")
    skills: Optional[str] = Field(default=None, description="Comma-separated list of skills - use actual skills mentioned in profile")
    languages: Optional[str] = Field(default="English", description="English, plus any other languages - list as comma-separated values")
    summary: Optional[str] = Field(default=None, description="Detailed professional summary (150-250 words) describing: 1) Area of expertise, 2) Years and depth of experience, 3) Types of organizations worked with, 4) Scale of responsibilities (team size, budget, geographic scope, etc.), 5) Key achievements, 6) Current status/availability if relevant. Example: 'HR leader with deep experience across HRBP, Reward/Compensation & Benefits and Talent roles, in both consulting and corporate contexts. Led large HR teams (>300 employees, $43m budget), covering country, regional and global roles (62 countries), and large businesses (>34,000 headcount). Currently providing interim reward consultancy advice, including through association with 3XO. Available for permanent roles at short notice (generalist or reward, in London, UK).'")
    readiness: Optional[str] = Field(default="Not Ready", description="Readiness of the candidate - set to 'Not Ready' for new candidates")

class PipelineData(BaseModel):
    # Fields matching the pipeline table in the Laravel implementation
    # Basic candidate information
    first_name: str = Field(description="First name of the candidate", min_length=1, max_length=50)
    last_name: str = Field(description="Last name of the candidate", min_length=1, max_length=50)
    middle_name: Optional[str] = Field(default=None, description="Middle name of the candidate if available")
    other_name: Optional[str] = Field(default=None, description="Other names or aliases of the candidate")
    gender: Optional[str] = Field(default=None, description="ONLY 'Male' or 'Female' (use best guess based on first and last name)")
    diverse: Optional[str] = Field(default=None, description="ONLY one of: Irish/Italian/Asian/German/English/Indian/French/Spanish/Other (infer likely ethnicity based on full name)")
    
    # Location information
    country: Optional[str] = Field(default=None, description="Country of residence - use full country name, not abbreviations")
    city: Optional[str] = Field(default=None, description="City of residence - use proper capitalization")
    location: Optional[str] = Field(default=None, description="City only, for display purposes")
    
    # Contact information
    contact_number: Optional[str] = Field(default="", description="Candidate's phone number if available, otherwise leave blank")
    email_address: Optional[str] = Field(default="", description="Candidate's email address if available, otherwise leave blank")
    
    # Professional information
    summary: Optional[str] = Field(default=None, description="Detailed professional summary (150-250 words) describing: 1) Area of expertise, 2) Years and depth of experience, 3) Types of organizations worked with, 4) Scale of responsibilities (team size, budget, geographic scope, etc.), 5) Key achievements, 6) Current status/availability if relevant. Example: 'HR leader with deep experience across HRBP, Reward/Compensation & Benefits and Talent roles, in both consulting and corporate contexts. Led large HR teams (>300 employees, $43m budget), covering country, regional and global roles (62 countries), and large businesses (>34,000 headcount). Currently providing interim reward consultancy advice, including through association with 3XO. Available for permanent roles at short notice (generalist or reward, in London, UK).'")
    linkedinURL: str = Field(description="LinkedIn profile URL - must start with 'https://www.linkedin.com/'")
    latest_role: str = Field(description="ONLY the most recent/current job title (extract just the specific role title without the company name)", min_length=2, max_length=100)
    company_id: Optional[int] = Field(default=0, description="ID of the company in the database")
    company_name: Optional[str] = Field(default="Not Specified", description="Current company name - use official company name", min_length=1, max_length=100) 
    start_date: Optional[str] = Field(default=None, description="Start date of current role - STRICTLY use YYYY-MM-DD format")
    end_date: Optional[str] = Field(default=None, description="null if still employed, YYYY-MM-DD if left role")
    tenure: Optional[int] = Field(default=0, description="Years in current role - must be a positive integer or zero", ge=0, le=50)
    function: Optional[str] = Field(default=None, description="Function area (e.g., Operations, Finance, Marketing, etc.)")
    division: Optional[str] = Field(default=None, description="Division within company - use actual division name if available")
    seniority: Optional[str] = Field(default=None, description="ONLY one of: Head/Manager/Director/VP/C-Suite/Other")
    exco: Optional[str] = Field(default="Non Exco", description="ONLY 'Exco' or 'Non Exco' for executive committee status")
    
    # Background information
    career_history: Optional[str] = Field(default=None, description="EXTRACT THE FULL CAREER HISTORY, INCLUDING ALL POSITIONS. Format as chronological list with dates, titles, companies, and locations")
    educational_history: Optional[str] = Field(default=None, description="Format as 'Institution, Field of Study - Degree Type · Graduation Year'")
    skills: Optional[str] = Field(default=None, description="Comma-separated list of skills - use actual skills mentioned in profile")
    languages: Optional[str] = Field(default="English", description="English, plus any other languages - list as comma-separated values")
    
    # Pipeline-specific fields
    plan_id: Optional[int] = Field(default=0, description="ID of the succession plan")
    people_id: Optional[int] = Field(default=0, description="ID of the candidate in the People table")
    people_type: str = Field(default="External-AI", description="Type of candidate - External-AI for candidates from external search")
    readiness: str = Field(default="Ready", description="Candidate readiness - Always 'Ready' for external candidates")
    other_tags: Optional[str] = Field(default=None, description="Other tags or notes")
    
    # Match scores
    role_match: float = Field(default=0.5, description="Role match score")
    skills_match: float = Field(default=0.4, description="Skills match score")
    location_match: float = Field(default=1.0, description="Location match score - default 1.0")
    gender_match: float = Field(default=1.0, description="Gender match score - default 1.0")
    tenure_match: float = Field(default=1.0, description="Tenure match score")
    education_match: float = Field(default=0.3, description="Education match score")
    total_score: float = Field(default=3.2, description="Total match score - sum of all scores except gender_match")

class Skill(BaseModel):
    """Model representing a candidate skill entry for database"""
    people_id: int = Field(default=0, description="ID of the candidate in the People table")
    skill_name: str = Field(description="Name of the skill")
    skill_type: str = Field(default="Other", description="Type of skill: Technical, Leadership, Communication, Financial, or Other")

class CareerHistory(BaseModel):
    """Model representing a career history entry for database"""
    people_id: int = Field(default=0, description="ID of the candidate in the People table")
    role: str = Field(description="Job title for this position")
    past_company_id: int = Field(default=0, description="ID of company for this position")
    company_name: str = Field(description="Name of company for this position")
    start_date: Optional[str] = Field(default=None, description="Start date in YYYY-MM-DD format")
    end_date: Optional[str] = Field(default=None, description="End date in YYYY-MM-DD format") 
    tenure: float = Field(default=0.0, description="Years in this position")

class CandidateProfile(BaseModel):
    people_data: PeopleData
    pipeline_data: PipelineData
    skills: List[Skill] = Field(default_factory=list, description="Parsed skills entries")
    career_history: List[CareerHistory] = Field(default_factory=list, description="Parsed career history entries")
    match_reasoning: str = Field(description="Detailed scoring breakdown explaining how each score was calculated")
    
    def __post_init__(self):
        # Copy values from people_data to pipeline_data with field name mapping
        if hasattr(self, 'people_data') and hasattr(self, 'pipeline_data'):
            # Map forename/surname from people_data to first_name/last_name in pipeline_data
            self.pipeline_data.first_name = self.people_data.forename
            self.pipeline_data.last_name = self.people_data.surname

class ProfileResponse(BaseModel):
    profiles: List[CandidateProfile]

# Additional models for standalone script functionality
class PlanData(BaseModel):
    """Model representing a succession plan's search requirements"""
    plan_name: str = Field(default="External Search Plan")
    plan_id: str = Field(default="standalone-search-001")
    target_roles: List[str] = Field(default_factory=list)
    alternative_roles_titles: List[str] = Field(default_factory=list)
    step_up_candidates: List[str] = Field(default_factory=list)
    companies: List[str] = Field(default_factory=list)
    gender: str = Field(default="Not required")
    country: List[str] = Field(default_factory=list)
    is_ethnicity_important: bool = Field(default=False)
    minimum_tenure: Optional[int] = Field(default=None)
    qualifications: List[str] = Field(default_factory=list)
    include_alumni: Optional[bool] = Field(default=None)
    enable_early_duplicate_detection: bool = Field(default=True)
    skills: List[str] = Field(default_factory=list)
    search_query: Optional[str] = Field(default=None)
    
class SearchResult(BaseModel):
    """Model representing a simplified search result"""
    name: str
    role: str 
    company: str
    url: str
    score: float
    reasoning: str

class ExternalSearchEngine:
    """
    Engine to perform external candidate searches using Exa API
    Implements the same search logic as the Laravel application
    """
    
    def __init__(self):
        """Initialize the search engine with API keys"""
        # Load environment variables
        load_env_vars()
        
        # Get API keys from environment
        self.exa_api_key = os.environ.get('EXA_API_KEY')
        self.openai_api_key = os.environ.get('OPENAI_API_KEY')
        
        # Validate API keys
        if not self.exa_api_key:
            logger.error("Error: EXA_API_KEY is not set in the .env file or environment")
            sys.exit(1)
            
        if not self.openai_api_key:
            logger.error("Error: OPENAI_API_KEY is not set in the .env file or environment")
            sys.exit(1)
            
        # Initialize clients
        self.exa = Exa(api_key=self.exa_api_key)
        
        # Initialize both sync and async OpenAI clients with instructor
        self.openai = instructor.patch(OpenAI(api_key=self.openai_api_key))
        self.openai_async = instructor.apatch(AsyncOpenAI(api_key=self.openai_api_key))
        
        # Set model to use
        self.model = os.environ.get('OPENAI_MODEL', 'gpt-4o')
        
        # Configure concurrency settings
        self.max_concurrent = int(os.environ.get('MAX_CONCURRENT_REQUESTS', 5))
        
        logger.info(f"External search engine initialized with model: {self.model}")
        logger.info(f"Async processing enabled with max concurrency: {self.max_concurrent}")
    
    def search_external_candidates(self, plan_data: Dict[str, Any], output_file="linkedin_profiles.json") -> List[Dict[str, Any]]:
        """
        Main method to execute external candidate search (synchronous wrapper)
        
        Args:
            plan_data: Dictionary containing search parameters
            output_file: Path to save results JSON
            
        Returns:
            List of candidate profiles
        """
        # Use asyncio.run to run the async implementation
        import asyncio
        return asyncio.run(self.search_external_candidates_async(plan_data, output_file))
            
    async def search_external_candidates_async(self, plan_data: Dict[str, Any], output_file="linkedin_profiles.json") -> List[Dict[str, Any]]:
        """
        Main method to execute external candidate search (async implementation)
        
        Args:
            plan_data: Dictionary containing search parameters
            output_file: Path to save results JSON
            
        Returns:
            List of candidate profiles
        """
        try:
            import time
            overall_start = time.time()
            
            # Validate and prepare plan data
            plan_data = self._validate_plan_data(plan_data)
            
            logger.info(f"Starting external search for plan '{plan_data['plan_name']}' (async mode)")
            
            # Get number of results to fetch - allow override via plan_data
            num_results = plan_data.get('num_results', 100)
            logger.info(f"Will process up to {num_results} search results")
            
            # Determine search query
            query = self._build_search_query(plan_data)
            logger.info(f"Using search query: {query}")
            
            # Execute the search
            search_results = self._perform_exa_search(query, num_results=num_results)
            
            if not search_results:
                logger.warning("No search results found")
                return []
                
            logger.info(f"Found {len(search_results)} search results")

            # EARLY DUPLICATE DETECTION - Filter out existing LinkedIn URLs before AI processing
            enable_early_duplicate_detection = plan_data.get('enable_early_duplicate_detection', True)

            if enable_early_duplicate_detection:
                logger.info(f"Starting enhanced early duplicate detection for {len(search_results)} profiles")
                deduplicated_results = self._filter_existing_linkedin_urls(search_results, plan_data)
                duplicates_filtered = len(search_results) - len(deduplicated_results)

                if duplicates_filtered > 0:
                    logger.info(f"ENHANCED EARLY DUPLICATE DETECTION: Filtered out {duplicates_filtered} LinkedIn profiles")
                    logger.info(f"COST SAVINGS: Avoided processing {duplicates_filtered} profiles through AI pipeline")
                    logger.info(f"EFFICIENCY GAIN: {(duplicates_filtered/len(search_results)*100):.1f}% reduction in AI processing")

                # Use deduplicated results for further processing
                search_results = deduplicated_results
            else:
                logger.info("Enhanced early duplicate detection disabled - processing all search results")
                duplicates_filtered = 0

            # Get batch size for processing - default to a reasonable value for async processing
            # For async, we want larger batches since we process them in parallel
            batch_size = plan_data.get('batch_size', 10)
            logger.info(f"Using batch size of {batch_size} for async profile processing")

            # Process the search results to generate profiles using async implementation
            all_profiles = []
            filtered_count = 0
            total_count = 0
            
            # Process in batches with async
            for batch_idx in range(0, len(search_results), batch_size):
                batch_end = min(batch_idx + batch_size, len(search_results))
                batch = search_results[batch_idx:batch_end]
                
                logger.info(f"Processing batch {batch_idx//batch_size + 1}/{(len(search_results) + batch_size - 1)//batch_size}: profiles {batch_idx+1}-{batch_end}")
                
                # Process batch with async
                batch_start = time.time()
                batch_profiles, batch_filtered = await self._process_search_results_batch_async(batch, plan_data)
                batch_time = time.time() - batch_start
                
                all_profiles.extend(batch_profiles)
                filtered_count += batch_filtered
                total_count += len(batch)
                
                # Calculate per-item time for this batch
                per_item_time = batch_time / len(batch) if len(batch) > 0 else 0
                effective_items_per_second = len(batch) / batch_time if batch_time > 0 else 0
                
                logger.info(f"Batch {batch_idx//batch_size + 1} completed in {batch_time:.2f}s: " +
                           f"{len(batch_profiles)} profiles generated, {batch_filtered} filtered out " +
                           f"({per_item_time:.2f}s per item, {effective_items_per_second:.2f} items/sec)")
            
            # Calculate final scores
            profiles = all_profiles
            scores_start = time.time()
            profiles = self._calculate_candidate_scores(profiles, plan_data)
            scores_time = time.time() - scores_start
            
            if not profiles:
                logger.warning("No profiles generated from search results")
                return []
                
            logger.info(f"Generated {len(profiles)} candidate profiles")
            
            # Save results to file if specified
            if output_file:
                response = ProfileResponse(profiles=profiles)

                # Calculate enhanced metrics for PHP service analysis
                original_search_count = len(search_results) + duplicates_filtered
                early_detection_time_ms = getattr(self, '_early_detection_time_ms', 0)
                duplicate_stats = getattr(self, '_duplicate_stats', {})

                # Create enhanced output data with metrics
                enhanced_output = {
                    "profiles": response.model_dump()["profiles"],
                    "metrics": {
                        "original_search_results": original_search_count,
                        "duplicates_filtered": duplicates_filtered,
                        "profiles_sent_to_ai": len(search_results),
                        "final_profiles_generated": len(profiles),
                        "early_detection_time_ms": early_detection_time_ms,
                        "total_processing_time": overall_time,
                        "search_query": plan_data.get('search_query', 'Generated query'),
                        "timestamp": time.time()
                    },
                    "duplicate_detection": duplicate_stats,
                    "search_metadata": {
                        "query": plan_data.get('search_query', 'Generated query'),
                        "num_results_requested": num_results,
                        "profiles_generated": len(profiles),
                        "timestamp": time.time()
                    }
                }

                # Make sure the output path is absolute
                if not os.path.isabs(output_file):
                    output_path = os.path.abspath(output_file)
                else:
                    output_path = output_file

                # Make sure the directory exists
                output_dir = os.path.dirname(output_path)
                os.makedirs(output_dir, exist_ok=True)
                logger.info(f"Using output path: {output_path} (directory: {output_dir})")

                logger.info(f"Saving enhanced profiles with metrics to: {output_path}")
                logger.info(f"Current working directory: {os.getcwd()}")

                try:
                    with open(output_path, "w") as f:
                        json.dump(enhanced_output, f, indent=2)

                    # No need to set permissions when writing to working directory

                    logger.info(f"Successfully saved enhanced profiles with metrics to {output_path}")
                except Exception as e:
                    logger.error(f"Error saving to {output_path}: {str(e)}")
            
            # Return simplified results
            simple_results = []
            for profile in profiles:
                simple_results.append({
                    "name": f"{profile.people_data.forename} {profile.people_data.surname}",
                    "role": profile.people_data.latest_role,
                    "company": profile.people_data.company_name,
                    "url": profile.people_data.linkedinURL,
                    "score": profile.pipeline_data.total_score,
                    "reasoning": profile.match_reasoning
                })
            
            # Log overall performance including enhanced early duplicate detection savings
            overall_time = time.time() - overall_start
            original_count = len(search_results) + duplicates_filtered  # Add back the filtered duplicates

            logger.info(f"ENHANCED PERFORMANCE SUMMARY:")
            logger.info(f"  • Original profiles from Exa: {original_count}")
            logger.info(f"  • Enhanced early duplicates filtered: {duplicates_filtered} ({(duplicates_filtered/original_count*100):.1f}%)" if original_count > 0 else "  • Enhanced early duplicates filtered: 0")
            logger.info(f"    ├─ Existing in people.linkedinURL: [logged separately above]")
            logger.info(f"    └─ Already in pipelines.people_id for this plan: [logged separately above]")
            logger.info(f"  • Profiles sent to AI processing: {total_count}")
            logger.info(f"  • Profiles filtered during AI processing: {filtered_count}")
            logger.info(f"  • Final candidate profiles generated: {len(profiles)}")
            logger.info(f"  • Total processing time: {overall_time:.2f}s")

            # Calculate cost savings from enhanced early duplicate detection
            if duplicates_filtered > 0:
                avg_processing_time_per_profile = overall_time / len(profiles) if len(profiles) > 0 else 2.0
                estimated_time_savings = duplicates_filtered * avg_processing_time_per_profile
                estimated_cost_savings = duplicates_filtered * 0.02  # Rough estimate: $0.02 per AI profile processing

                logger.info(f"COST SAVINGS FROM ENHANCED EARLY DUPLICATE DETECTION:")
                logger.info(f"  • Estimated time saved: {estimated_time_savings:.2f}s")
                logger.info(f"  • Estimated cost saved: ${estimated_cost_savings:.2f}")
                logger.info(f"  • Processing efficiency gain: {(duplicates_filtered/original_count*100):.1f}%")
                logger.info(f"  • Eliminated redundant pipeline checks: {duplicates_filtered} checks avoided")

            # Calculate performance metrics
            if len(profiles) > 0:
                logger.info(f"ADDITIONAL PERFORMANCE METRICS:")
                logger.info(f"  • Average time per profile: {overall_time/len(profiles):.2f}s (includes all processing)")

                # Estimate speedup (assume 15s per profile for sequential processing)
                sequential_estimate = total_count * 15  # 15 seconds per profile is a conservative estimate
                speedup = sequential_estimate / overall_time if overall_time > 0 else 0
                logger.info(f"  • Estimated speedup: {speedup:.1f}x faster than sequential processing")
            
            return simple_results
            
        except Exception as e:
            logger.error(f"Error in search_external_candidates_async: {str(e)}", exc_info=True)
            # Include error information in the output file
            if output_file:
                try:
                    # Make sure the output path is absolute
                    if not os.path.isabs(output_file):
                        output_path = os.path.abspath(output_file)
                    else:
                        output_path = output_file
                    
                    # Make sure the directory exists
                    output_dir = os.path.dirname(output_path)
                    os.makedirs(output_dir, exist_ok=True)
                    
                    logger.info(f"Saving error information to: {output_path}")
                    logger.info(f"Current working directory: {os.getcwd()}")
                    
                    error_data = {
                        "error": str(e),
                        "traceback": traceback.format_exc(),
                        "profiles": []
                    }
                    
                    with open(output_path, "w") as f:
                        json.dump(error_data, f, indent=2)
                    
                    # No need to set permissions when writing to working directory
                        
                    logger.info(f"Saved error information to {output_path}")
                except Exception as write_err:
                    logger.error(f"Failed to write error information to {output_path}: {str(write_err)}")
            return []
    
    def _validate_plan_data(self, plan_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and prepare plan data by ensuring all required fields are present
        and setting default values for optional fields
        
        Args:
            plan_data: Dictionary containing search parameters
            
        Returns:
            Dictionary with validated and prepared plan data
        """
        # Required fields
        required_fields = ['target_roles']
        
        # Check required fields
        for field in required_fields:
            if field not in plan_data or not plan_data[field]:
                logger.error(f"Missing required field: {field}")
                raise ValueError(f"Missing required field: {field}")
        
        # Set defaults for optional fields
        defaults = {
            'plan_name': 'External Search Plan',
            'plan_id': f'standalone-{int(time.time())}',
            'alternative_roles_titles': [],
            'step_up_candidates': [],
            'companies': [],
            'gender': 'Not required',
            'country': [],
            'is_ethnicity_important': False,
            'minimum_tenure': None,
            'qualifications': [],
            'skills': [],
            'enable_early_duplicate_detection': True
            # Note: include_alumni is NOT defaulted - it must be explicitly set by the user
        }
        
        # Apply defaults where needed
        for key, value in defaults.items():
            if key not in plan_data or plan_data[key] is None:
                plan_data[key] = value

        # Validate that include_alumni is explicitly set
        if 'include_alumni' not in plan_data or plan_data['include_alumni'] is None:
            logger.warning(f"Alumni preference not set for plan '{plan_data['plan_name']}' - this may cause filtering issues")
            # Don't set a default - let the filtering logic handle this

        logger.info(f"Plan data validated and prepared: {plan_data['plan_name']}")
        logger.info(f"Alumni preference setting: {plan_data.get('include_alumni', 'NOT SET')}")
        return plan_data
    
    def _build_search_query(self, plan_data: Dict[str, Any]) -> str:
        """
        Build a search query based on the plan data
        
        Args:
            plan_data: Dictionary containing search parameters
            
        Returns:
            String containing the search query
        """
        # If user provided a custom search query, use it directly
        if 'search_query' in plan_data and plan_data['search_query']:
            return plan_data['search_query']
        
        # Otherwise, build a query from plan components
        roles = plan_data['target_roles']
        if not roles:
            raise ValueError("No target roles specified for search query")
            
        # Format target roles with quotes and join with OR, wrapped in parentheses for proper precedence
        quoted_roles = ['"' + role + '"' for role in roles]
        role_query = " OR ".join(quoted_roles)

        # Wrap role alternatives in parentheses for proper logical grouping
        # This ensures queries like ("Chief Risk Officer" OR "CRO") AND ("HSBC") AND ("United Kingdom")
        # instead of "Chief Risk Officer" OR "CRO" AND ("HSBC") AND ("United Kingdom")
        role_query = f"({role_query})"

        # Add companies if specified - with quotes and OR between companies
        company_part = ""
        if plan_data['companies'] and plan_data['companies'] != ['none']:
            quoted_companies = ['"' + company + '"' for company in plan_data['companies']]
            company_query = " OR ".join(quoted_companies)
            company_part = f" AND ({company_query})"

        # Add countries if specified - with quotes and OR between countries
        location_part = ""
        if plan_data['country'] and plan_data['country'] != ['none']:
            quoted_countries = ['"' + country + '"' for country in plan_data['country']]
            country_query = " OR ".join(quoted_countries)
            location_part = f" AND ({country_query})"

        # Combine parts into final query with AND logic, proper parentheses grouping
        query = f"{role_query}{company_part}{location_part}"
        return query
    
    def _perform_exa_search(self, query: str, num_results: int = 100) -> List[Dict[str, Any]]:
        """
        Perform search using Exa API
        
        Args:
            query: Search query string
            num_results: Number of results to return (default: 10)
            
        Returns:
            List of search results
        """
        try:
            logger.info(f"Executing Exa search with query: {query}")
            
            # Setup retry mechanism
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    # Execute the search with parameters supported by the Python SDK
                    # Note: The PHP implementation uses 'contents' parameter in direct HTTP API
                    # but Python SDK doesn't support this parameter
                    search_response = self.exa.search_and_contents(
                        query=query,
                        num_results=num_results,
                        text=True,  # Get the full text content
                        type="auto",  # Match PHP's 'auto' type
                        category="linkedin profile",  # Match PHP's category
                        use_autoprompt=True,  # Improve search results with autoprompt
                        include_domains=["linkedin.com"]  # Focus only on LinkedIn domain
                    )
                    
                    if not search_response or not hasattr(search_response, 'results') or not search_response.results:
                        logger.warning(f"Empty or invalid response from Exa (attempt {attempt+1})")
                        if attempt < max_retries - 1:
                            time.sleep(2)  # Wait before retry
                            continue
                        return []
                    
                    logger.info(f"Received {len(search_response.results)} results from Exa")
                    return search_response.results
                    
                except Exception as e:
                    logger.error(f"Error during Exa search (attempt {attempt+1}): {str(e)}")
                    if attempt < max_retries - 1:
                        time.sleep(2)  # Wait before retry
                    else:
                        logger.error("All retry attempts failed")
                        return []
            
            return []
            
        except Exception as e:
            logger.error(f"Error in _perform_exa_search: {str(e)}", exc_info=True)
            return []
    
    def _preliminary_filter_candidates(self, search_results: List[Dict[str, Any]], plan_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Filter candidate profiles using LLM before full processing
        
        Args:
            search_results: List of search results from Exa
            plan_data: Dictionary containing search parameters
            
        Returns:
            List of filtered search results to process further
        """
        logger.info(f"Starting preliminary LLM filtering of {len(search_results)} profiles")
        
        # Batch profiles to reduce API calls
        batch_size = 10
        batches = [search_results[i:i+batch_size] for i in range(0, len(search_results), batch_size)]
        
        # Track kept URLs and filtering statistics
        kept_results = []
        stats = {"total": len(search_results), "kept": 0, "filtered": 0}
        
        # Create output directory for temporary files if needed
        timestamp = int(time.time())
        # Access output_file from plan_data or set a default
        self.output_file = plan_data.get('output_file', 'linkedin_profiles.json')
        output_dir = os.path.dirname(self.output_file)
        os.makedirs(output_dir, exist_ok=True)
        
        # Process each batch
        for i, batch in enumerate(batches):
            logger.info(f"Processing LLM filtering batch {i+1}/{len(batches)} ({len(batch)} profiles)")
            
            # Prepare profiles data for filtering - using raw text from Exa results
            profiles_data = []
            for result in batch:
                try:
                    # Just send the raw result content directly to the LLM
                    profiles_data.append({
                        "url": result.url,
                        "title": result.title,
                        "full_content": result.text
                    })
                except Exception as e:
                    logger.error(f"Error processing data for {result.url}: {str(e)}")
            
            if not profiles_data:
                logger.warning(f"No valid profiles in batch {i+1}, skipping")
                continue
                
            # Prepare the prompt with batch data
            prompt = self._create_filtering_prompt(profiles_data, plan_data)
            
            # Call LLM for filtering decision
            try:
                response = self.openai.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.2,
                    response_format={"type": "json_object"}
                )
                
                # Parse the response
                content = response.choices[0].message.content
                results = json.loads(content)
                
                # Process filtering decisions
                for profile in results.get("profiles", []):
                    url = profile.get("url")
                    decision = profile.get("decision")
                    if not url or not decision:
                        continue
                        
                    if decision == "KEEP":
                        # Find the original result object with this URL
                        matching_result = next((r for r in batch if r.url == url), None)
                        if matching_result:
                            kept_results.append(matching_result)
                            stats["kept"] += 1
                            logger.info(f"Keeping profile: {url} - {profile.get('matching_info', {}).get('company_match_reason', 'No reason provided')}")
                    else:
                        stats["filtered"] += 1
                        logger.info(f"Filtering out: {url} - {profile.get('reasoning', 'No reason provided')}")
                
            except Exception as e:
                logger.error(f"Error in LLM filtering: {str(e)}")
                # On error, keep all profiles in this batch as a fallback
                for profile_data in profiles_data:
                    matching_result = next((r for r in batch if r.url == profile_data["url"]), None)
                    if matching_result:
                        kept_results.append(matching_result)
                        stats["kept"] += 1
                        logger.warning(f"Keeping profile due to error: {profile_data['url']}")
                
            # Add delay to avoid rate limits
            time.sleep(1)
        
        # Log filtering statistics
        filter_rate = (stats["filtered"] / stats["total"]) * 100 if stats["total"] > 0 else 0
        logger.info(f"Preliminary filtering complete: {stats['kept']}/{stats['total']} kept ({filter_rate:.1f}% filtered)")
        
        # Save filtered URLs to temporary file for debugging/recovery
        filtered_file = os.path.join(output_dir, f"filtered_urls_{timestamp}.json")
        with open(filtered_file, "w") as f:
            json.dump({
                "kept_urls": [r.url for r in kept_results],
                "stats": stats,
                "timestamp": timestamp
            }, f)
            
        logger.info(f"Saved filtered URLs to {filtered_file}")
        
        return kept_results
    
    # No longer needed - we use the raw text from Exa results directly
    
    def _create_filtering_prompt(self, profiles_data: List[Dict[str, Any]], plan_data: Dict[str, Any]) -> str:
        """
        Create prompt for LLM filtering using full Exa search result content
        
        Args:
            profiles_data: List of profiles with full content from Exa results
            plan_data: Dictionary containing search parameters
            
        Returns:
            String containing the prompt
        """
        # Format plan requirements for the prompt
        plan_requirements = json.dumps({
            "target_roles": plan_data.get('target_roles', []),
            "companies": plan_data.get('companies', []),
            "step_up_candidates": plan_data.get('step_up_candidates', []),
            "gender": plan_data.get('gender', 'Not required'),
            "country": plan_data.get('country', []),
            "minimum_tenure": plan_data.get('minimum_tenure', 0),
            "include_alumni": plan_data.get('include_alumni')  # No default - must be explicitly set
        }, indent=2)
        
        # Extract only URLs and titles for listing in the prompt to save tokens
        profile_summaries = [{'url': p["url"], 'title': p.get("title", "")} for p in profiles_data]
        urls_list = json.dumps(profile_summaries, indent=2)
        
        # Prepare full content for each profile (will be appended to the prompt)
        profiles_content = ""
        for i, profile in enumerate(profiles_data):
            profiles_content += f"\n\n### PROFILE {i+1}: {profile['url']}\n{profile.get('title', '')}\n\n"
            profiles_content += profile.get('full_content', 'No content available')
            profiles_content += "\n\n---"
        
        # Create the prompt using the template
        prompt = f"""You are an AI assistant that filters and evaluates candidate profiles based on LinkedIn profile content and succession plan requirements. Your task is to analyze each candidate profile and determine its relevance to the succession plan based on specific criteria.

# Profiles to Analyze
{urls_list}

# Plan Requirements
{plan_requirements}

# Instructions

You will be provided with the full text content of several LinkedIn profiles. Analyze each profile and identify whether the candidate is relevant based on the following criteria:

## 1. Company Matching (MANDATORY)
- Company matching requirements depend on the alumni preference setting:

  **If include_alumni is TRUE:**
  - Candidates MUST either:
    a) Currently work at one of the target companies, OR
    b) Have previously worked at one of the target companies (within the last 5 years)

  **If include_alumni is FALSE:**
  - Candidates MUST currently work at one of the target companies
  - Do NOT include candidates who only have previous experience at target companies
  - Only current employees are acceptable

  **If include_alumni is NULL or not specified:**
  - This indicates a configuration error - FILTER OUT all candidates and note the missing preference

- This is a hard requirement - if a candidate does not match the appropriate condition based on alumni preference, they MUST be filtered out regardless of other criteria
- Be flexible with company variations:
  - Match different versions of company names (e.g., "Google AI" vs "Google DeepMind" vs "Google")
  - Consider subsidiaries and parent-child relationships
- Document clearly whether the match is based on current employment or previous employment at target companies
- When include_alumni is FALSE, explicitly state if a candidate was filtered out for being an alumnus rather than current employee

## 2. Role Matching (Flexible but Focused)
- Be flexible with role variations and avoid overfitting to specific titles:
  - Match candidates with titles that are functionally equivalent or transferable (e.g., "Software Engineer AI/ML" vs "Machine Learning Engineer" vs "AI Developer")
  - Consider standardization of titles (e.g., "Vice President" = "VP", "Manager" = "Head of")
  - Look at reversed word order (e.g., "Director of Sales" vs "Sales Director")
  - Focus on FUNCTIONAL SKILLS rather than exact title matches
  - Accept candidates from adjacent/related fields that require similar competencies
  - REJECT candidates whose roles indicate significantly higher seniority than the target role
  - Follow this seniority hierarchy (highest to lowest): C-level > VP/Director > Manager/Head > Senior > Regular > Junior/Associate
  - For example, FILTER OUT a "Data Science Manager" when the target role is "Data Scientist" (step-down match)
  - KEEP a "Data Analyst" when the target role is "Data Scientist" (step-up match)
  - KEEP candidates from related fields (e.g., "Business Analyst" for "Data Scientist" if they have relevant technical skills)

## 3. Gender Matching (If Specified)
- If gender is specified in the plan, strictly enforce this requirement
- Otherwise, include candidates of any gender

## 4. Tenure Evaluation (CRITICAL - Cumulative Experience)
- Calculate the candidate's TOTAL CUMULATIVE experience in similar roles across ALL companies
- Sum up experience from multiple positions with similar titles/functions across their entire career
- A candidate might have worked as "Data Scientist" at Google for 2 years, then "Senior Data Scientist" at Microsoft for 3 years - this counts as 5 years total experience
- Include variations of the same role (e.g., "Data Scientist", "Senior Data Scientist", "Principal Data Scientist" all count toward data science experience)
- Only count experience that is relevant to the target role requirements
- If minimum tenure is specified, the candidate MUST have at least that many years of cumulative relevant experience

## 5. Location Requirements (STRICT)
- If specific countries/locations are specified in the plan requirements, candidates MUST currently be located in those countries
- Do NOT accept candidates who previously worked in the target location but are now elsewhere
- For example, if the plan requires "United Kingdom" candidates:
  - KEEP: Currently working in London, UK
  - FILTER: Previously worked in London but now working in New York
- Current location takes precedence over work history
- If no location requirements are specified, accept candidates from any location

## 6. Skills Relevance
- Evaluate how the candidate's skills match the required skills for the role

For each profile, determine a "KEEP" or "FILTER" decision based on these criteria, with company matching being a mandatory requirement.

# Output Format
Provide your analysis as a JSON array with the following structure for each candidate:

```json
{{
  "profiles": [
    {{
      "url": "[LinkedIn URL]",
      "name": "[Candidate name]",
      "decision": "KEEP/FILTER",
      "reasoning": "[Detailed explanation of why the candidate should be kept or filtered, addressing each of the criteria above]",
      "current_role": "[Extracted current role]",
      "current_company": "[Extracted current company]",
      "matching_info": {{
        "company_match": true/false,
        "company_match_reason": "[Explanation of company matching - clearly state if it's a current or previous employment match and whether alumni preference is satisfied]",
        "role_match": true/false,
        "role_match_reason": "[Explanation of role matching, including seniority comparison and functional transferability]",
        "location_match": true/false,
        "location_match_reason": "[Explanation of current location vs required location - be strict about current location]",
        "gender_match": true/false,
        "gender_match_reason": "[Explanation of gender matching]",
        "tenure_match": true/false,
        "tenure_match_reason": "[Explanation of CUMULATIVE tenure evaluation across all relevant roles and companies]",
        "skills_match": true/false,
        "skills_match_reason": "[Explanation of skills relevance]"
      }}
    }}
  ]
}}
```

Remember:
1. Company matching is MANDATORY - only keep candidates who currently work at OR previously worked at target companies (based on alumni preference)
2. If company_match is false, the decision MUST be "FILTER" regardless of other criteria
3. Location matching is STRICT - candidates must currently be in the required location, not just previously
4. Seniority hierarchy must be respected - FILTER OUT candidates with roles MORE senior than target roles
5. Role matching should be flexible and avoid overfitting - focus on functional transferability
6. Calculate CUMULATIVE tenure across all relevant roles and companies
7. Be strict on gender if specified
8. Make a clear KEEP/FILTER decision for each candidate
9. Provide detailed reasoning with emphasis on company matching, current location, and cumulative experience

# Full Profile Content
{profiles_content}
"""
        
        return prompt

    def _process_search_results(self, search_results: List[Dict[str, Any]], plan_data: Dict[str, Any]) -> List[CandidateProfile]:
        """
        Process search results to generate candidate profiles

        Args:
            search_results: List of search results from Exa
            plan_data: Dictionary containing search parameters

        Returns:
            List of CandidateProfile objects
        """
        import time
        profiles = []

        # EARLY DUPLICATE DETECTION - Filter out existing LinkedIn URLs before AI processing
        enable_early_duplicate_detection = plan_data.get('enable_early_duplicate_detection', True)

        if enable_early_duplicate_detection:
            logger.info(f"Starting early duplicate detection for {len(search_results)} profiles")
            deduplicated_results = self._filter_existing_linkedin_urls(search_results, plan_data)
            duplicates_filtered = len(search_results) - len(deduplicated_results)

            if duplicates_filtered > 0:
                logger.info(f"EARLY DUPLICATE DETECTION: Filtered out {duplicates_filtered} existing LinkedIn profiles")
                logger.info(f"COST SAVINGS: Avoided processing {duplicates_filtered} profiles through AI pipeline (~{duplicates_filtered * 20}% cost reduction)")
        else:
            logger.info("Early duplicate detection disabled - processing all search results")
            deduplicated_results = search_results
            duplicates_filtered = 0

        # Check if LLM filtering is enabled (default to enabled)
        use_llm_filtering = plan_data.get('use_llm_filtering', True)

        if use_llm_filtering:
            # Run preliminary LLM filtering to reduce processing load
            logger.info("LLM filtering enabled - running preliminary candidate filtering")
            filtered_results = self._preliminary_filter_candidates(deduplicated_results, plan_data)
        else:
            # Skip LLM filtering
            logger.info("LLM filtering disabled - processing all search results")
            filtered_results = deduplicated_results
        
        # Track performance metrics
        total_profiles = len(filtered_results)
        filtered_out = 0
        process_start = time.time()
        
        # Get batch size from plan_data, default to processing one at a time
        batch_size = int(plan_data.get('batch_size', 1))
        
        # Process in batches or individually based on batch_size
        if batch_size <= 1:
            # Standard sequential processing
            logger.info(f"Processing {len(filtered_results)} profiles individually")
            
            for i, result in enumerate(filtered_results):
                try:
                    logger.info(f"Processing result {i+1}/{len(filtered_results)}: {result.url}")
                    
                    # Use the text directly from Exa search results
                    content_start = time.time()
                    content = getattr(result, 'text', None)
                    content_time = time.time() - content_start
                    
                    if not content:
                        logger.warning(f"No text content found for {result.url}")
                        continue
                    
                    # Generate profile from content
                    profile_start = time.time()
                    profile = self._generate_profile(result.url, content, plan_data)
                    profile_time = time.time() - profile_start
                    
                    # Track if profile was filtered out by prefiltering
                    if not profile and content:
                        filtered_out += 1
                        logger.info(f"Profile filtered out for {result.url} - saved {profile_time:.2f}s of processing time")
                    
                    if profile:
                        profiles.append(profile)
                        logger.info(f"Successfully processed profile for {result.url} in {profile_time:.2f}s")
                    
                except Exception as e:
                    logger.error(f"Error processing result {i+1}: {str(e)}")
                    continue
        else:
            # Batch processing
            logger.info(f"Processing {len(filtered_results)} profiles in batches of {batch_size}")
            
            # Process in batches
            for batch_idx in range(0, len(filtered_results), batch_size):
                batch_end = min(batch_idx + batch_size, len(filtered_results))
                batch = filtered_results[batch_idx:batch_end]
                
                logger.info(f"Processing batch {batch_idx//batch_size + 1}/{(len(filtered_results) + batch_size - 1)//batch_size}: profiles {batch_idx+1}-{batch_end}")
                
                # Process batch
                batch_start = time.time()
                batch_profiles, batch_filtered = self._process_search_results_batch(batch, plan_data)
                batch_time = time.time() - batch_start
                
                profiles.extend(batch_profiles)
                filtered_out += batch_filtered
                
                logger.info(f"Batch {batch_idx//batch_size + 1} completed in {batch_time:.2f}s: {len(batch_profiles)} profiles generated, {batch_filtered} filtered out")
        
        # Calculate final scores
        scores_start = time.time()
        final_profiles = self._calculate_candidate_scores(profiles, plan_data)
        scores_time = time.time() - scores_start
        
        # Log performance metrics including early duplicate detection savings
        total_time = time.time() - process_start
        profiles_processed = len(profiles)
        original_count = len(search_results)
        early_duplicates_filtered = original_count - len(deduplicated_results)

        if total_profiles > 0:
            filter_rate = (filtered_out / total_profiles) * 100
            early_duplicate_rate = (early_duplicates_filtered / original_count) * 100 if original_count > 0 else 0

            logger.info(f"PERFORMANCE SUMMARY:")
            logger.info(f"  • Original profiles from Exa: {original_count}")
            logger.info(f"  • Early duplicates filtered: {early_duplicates_filtered} ({early_duplicate_rate:.1f}%)")
            logger.info(f"  • Profiles sent to AI processing: {len(deduplicated_results)}")
            logger.info(f"  • Profiles filtered by prefiltering: {filtered_out} ({filter_rate:.1f}%)")
            logger.info(f"  • Final candidate profiles generated: {profiles_processed}")
            logger.info(f"  • Total processing time: {total_time:.2f}s")
            logger.info(f"  • Score calculation time: {scores_time:.2f}s")

            # Calculate cost savings from early duplicate detection
            if early_duplicates_filtered > 0:
                avg_processing_time_per_profile = total_time / profiles_processed if profiles_processed > 0 else 2.0
                estimated_time_savings = early_duplicates_filtered * avg_processing_time_per_profile
                estimated_cost_savings = early_duplicates_filtered * 0.02  # Rough estimate: $0.02 per AI profile processing

                logger.info(f"COST SAVINGS FROM EARLY DUPLICATE DETECTION:")
                logger.info(f"  • Estimated time saved: {estimated_time_savings:.2f}s")
                logger.info(f"  • Estimated cost saved: ${estimated_cost_savings:.2f}")
                logger.info(f"  • Processing efficiency gain: {early_duplicate_rate:.1f}%")

            if filtered_out > 0:
                estimated_prefilter_savings = filtered_out * (total_time / profiles_processed) if profiles_processed > 0 else 0
                logger.info(f"ADDITIONAL SAVINGS FROM PREFILTERING: {estimated_prefilter_savings:.2f}s")
        
        return final_profiles

    def _filter_existing_linkedin_urls(self, search_results: List[Dict[str, Any]], plan_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Filter out LinkedIn URLs that already exist in the database before AI processing

        Args:
            search_results: List of search results from Exa
            plan_data: Dictionary containing search parameters

        Returns:
            List of search results with existing LinkedIn URLs removed
        """
        try:
            # Extract LinkedIn URLs from search results
            linkedin_urls = []
            url_to_result_map = {}

            for result in search_results:
                url = getattr(result, 'url', None)
                if url and 'linkedin.com' in url:
                    linkedin_urls.append(url)
                    url_to_result_map[url] = result

            if not linkedin_urls:
                logger.info("No LinkedIn URLs found in search results")
                return search_results

            logger.info(f"Checking {len(linkedin_urls)} LinkedIn URLs for duplicates")

            # Create a temporary file to pass URLs to PHP for database checking
            import tempfile
            import json

            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
                json.dump({
                    'linkedin_urls': linkedin_urls,
                    'plan_id': plan_data.get('plan_id', 'unknown')
                }, temp_file)
                temp_file_path = temp_file.name

            # Call PHP script to check for existing URLs
            import subprocess
            import os

            # Get the path to the PHP duplicate checker script
            script_dir = os.path.dirname(os.path.abspath(__file__))
            php_script_path = os.path.join(script_dir, '..', '..', 'app', 'Scripts', 'check_duplicate_linkedin.php')

            try:
                # Execute PHP script to check duplicates
                result = subprocess.run([
                    'php', php_script_path, temp_file_path
                ], capture_output=True, text=True, timeout=30)

                if result.returncode == 0:
                    # Parse the enhanced response
                    response_data = json.loads(result.stdout)
                    existing_urls = set(response_data.get('existing_urls', []))
                    people_duplicates = response_data.get('people_duplicates', 0)
                    pipeline_duplicates = response_data.get('pipeline_duplicates', 0)
                    processing_time = response_data.get('processing_time_ms', 0)

                    # Store metrics for PHP service analysis
                    self._early_detection_time_ms = processing_time
                    self._duplicate_stats = {
                        'people_duplicates': people_duplicates,
                        'pipeline_duplicates': pipeline_duplicates,
                        'total_duplicates': len(existing_urls),
                        'processing_time_ms': processing_time,
                        'people_existing_urls': response_data.get('people_existing_urls', []),
                        'pipeline_existing_urls': response_data.get('pipeline_existing_urls', [])
                    }

                    # Log detailed breakdown
                    logger.info(f"ENHANCED EARLY DUPLICATE DETECTION: Detailed breakdown")
                    logger.info(f"  • Found {people_duplicates} existing profiles in people.linkedinURL")
                    logger.info(f"  • Found {pipeline_duplicates} profiles already in pipelines.people_id for plan_id {plan_data.get('plan_id', 'unknown')}")
                    logger.info(f"  • Total filtered: {len(existing_urls)} profiles ({people_duplicates} existing + {pipeline_duplicates} in pipeline)")
                    logger.info(f"  • Database query time: {processing_time}ms")

                    # Filter out existing URLs
                    filtered_results = []
                    for result in search_results:
                        url = getattr(result, 'url', None)
                        if url not in existing_urls:
                            filtered_results.append(result)

                    # Clean up temp file
                    os.unlink(temp_file_path)

                    return filtered_results

                else:
                    logger.error(f"PHP duplicate check failed: {result.stderr}")
                    # Fall back to processing all results if duplicate check fails
                    os.unlink(temp_file_path)
                    return search_results

            except subprocess.TimeoutExpired:
                logger.error("PHP duplicate check timed out after 30 seconds")
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
                return search_results
            except subprocess.CalledProcessError as e:
                logger.error(f"PHP duplicate check process error: {e}")
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
                return search_results
            except Exception as e:
                logger.error(f"Error executing PHP duplicate check: {str(e)}")
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
                return search_results

        except Exception as e:
            logger.error(f"Error in early duplicate detection: {str(e)}")
            # Fall back to processing all results if duplicate detection fails
            return search_results

    def _process_search_results_batch(self, batch_results: List[Dict[str, Any]], plan_data: Dict[str, Any]) -> tuple:
        """
        Process a batch of search results to generate candidate profiles (synchronous version)
        
        Args:
            batch_results: List of search results from Exa for this batch
            plan_data: Dictionary containing search parameters
            
        Returns:
            Tuple of (list of CandidateProfile objects, count of filtered profiles)
        """
        import time
        batch_profiles = []
        batch_contents = []
        batch_urls = []
        filtered_count = 0
        
        # Use text content directly from Exa search results
        for result in batch_results:
            try:
                # Use text directly from the Exa search result
                content = getattr(result, 'text', None)
                if not content:
                    logger.warning(f"No text content found for {result.url}")
                    continue
                
                batch_contents.append(content)
                batch_urls.append(result.url)
                
            except Exception as e:
                logger.error(f"Error processing result for {result.url}: {str(e)}")
                continue
        
        # Then process all profiles
        for i, (url, content) in enumerate(zip(batch_urls, batch_contents)):
            try:
                logger.info(f"Processing content for URL: {url}")
                
                # Generate profile from content
                profile_start = time.time()
                profile = self._generate_profile(url, content, plan_data)
                profile_time = time.time() - profile_start
                
                # Track if profile was filtered out
                if not profile:
                    filtered_count += 1
                    logger.info(f"Profile filtered out for {url}")
                    
                if profile:
                    batch_profiles.append(profile)
                    logger.info(f"Successfully processed profile for {url} in {profile_time:.2f}s")
                
            except Exception as e:
                logger.error(f"Error processing content for {url}: {str(e)}")
                continue
                
        return batch_profiles, filtered_count
        
    async def _process_search_results_batch_async(self, batch_results: List[Dict[str, Any]], plan_data: Dict[str, Any]) -> tuple:
        """
        Process a batch of search results to generate candidate profiles (async version)
        
        Args:
            batch_results: List of search results from Exa for this batch
            plan_data: Dictionary containing search parameters
            
        Returns:
            Tuple of (list of CandidateProfile objects, count of filtered profiles)
        """
        import time
        batch_profiles = []
        batch_contents = []
        batch_urls = []
        filtered_count = 0
        
        # First fetch all content 
        # Use text content directly from Exa search results
        for result in batch_results:
            try:
                # Use text directly from the Exa search result
                content = getattr(result, 'text', None)
                if not content:
                    logger.warning(f"No text content found for {result.url}")
                    continue
                
                batch_contents.append(content)
                batch_urls.append(result.url)
                
            except Exception as e:
                logger.error(f"Error processing result for {result.url}: {str(e)}")
                continue
        
        # Setup parallel processing with rate limiting
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def process_profile_with_semaphore(url, content):
            async with semaphore:  # Limit concurrent requests
                logger.info(f"Processing content for URL: {url}")
                profile_start = time.time()
                
                try:
                    profile = await self._generate_profile_async(url, content, plan_data)
                    profile_time = time.time() - profile_start
                    
                    if profile:
                        logger.info(f"Successfully processed profile for {url} in {profile_time:.2f}s")
                        return profile, False  # Not filtered
                    else:
                        logger.info(f"Profile filtered out for {url}")
                        return None, True  # Filtered
                except Exception as e:
                    logger.error(f"Error in async profile processing for {url}: {str(e)}")
                    return None, False
        
        # Create tasks for all profiles
        tasks = [process_profile_with_semaphore(url, content) 
                for url, content in zip(batch_urls, batch_contents)]
        
        # Process in parallel with asyncio.gather
        batch_start = time.time()
        results = await asyncio.gather(*tasks)
        batch_time = time.time() - batch_start
        
        # Process results
        for result, filtered in results:
            if result:
                batch_profiles.append(result)
            if filtered:
                filtered_count += 1
        
        logger.info(f"Async batch processing completed in {batch_time:.2f}s - " +
                    f"Processed {len(batch_urls)} profiles with {len(batch_profiles)} generated")
                
        return batch_profiles, filtered_count
    
    # No longer needed - we use the raw text from Exa results directly
    
    
    def _generate_profile(self, url: str, content: str, plan_data: Dict[str, Any]) -> Optional[CandidateProfile]:
        """
        Generate a candidate profile from LinkedIn profile content (synchronous version)
        
        Args:
            url: LinkedIn profile URL
            content: LinkedIn profile content
            plan_data: Dictionary containing search parameters
            
        Returns:
            CandidateProfile object or None if generation fails
        """
        try:
            # Prefilter content to check if it contains target roles, alternative roles, or step-up roles,
            # and target companies before proceeding with expensive LLM processing
            if not self._should_process_profile(content, plan_data):
                logger.info(f"Skipping profile {url} due to prefiltering - no relevant roles or companies found")
                return None
            
            # Create prompt for the LLM
            prompt = self._create_profile_prompt(url, content, plan_data)
            
            # Call the LLM to generate the profile (synchronous)
            profile = self.openai.chat.completions.create(
                model=self.model,
                response_model=CandidateProfile,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                temperature=0.2
            )
            
            # Process career history and skills
            self._process_career_history(profile)
            self._process_skills(profile)
            
            return profile
            
        except Exception as e:
            logger.error(f"Error generating profile for {url}: {str(e)}")
            return None
            
    async def _generate_profile_async(self, url: str, content: str, plan_data: Dict[str, Any]) -> Optional[CandidateProfile]:
        """
        Generate a candidate profile from LinkedIn profile content (async version)
        
        Args:
            url: LinkedIn profile URL
            content: LinkedIn profile content
            plan_data: Dictionary containing search parameters
            
        Returns:
            CandidateProfile object or None if generation fails
        """
        try:
            # Prefilter content to check if it contains target roles, alternative roles, or step-up roles,
            # and target companies before proceeding with expensive LLM processing
            if not self._should_process_profile(content, plan_data):
                logger.info(f"Skipping profile {url} due to prefiltering - no relevant roles or companies found")
                return None
            
            # Create prompt for the LLM
            prompt = self._create_profile_prompt(url, content, plan_data)
            
            # Call the LLM to generate the profile (async)
            profile = await self.openai_async.chat.completions.create(
                model=self.model,
                response_model=CandidateProfile,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                temperature=0.2
            )
            
            # Process career history and skills
            self._process_career_history(profile)
            self._process_skills(profile)
            
            return profile
            
        except Exception as e:
            logger.error(f"Error generating profile for {url}: {str(e)}")
            return None
            
    def _should_process_profile(self, content: str, plan_data: Dict[str, Any]) -> bool:
        """
        Prefilter profiles based on content to avoid expensive LLM processing
        for candidates that don't match basic requirements
        
        Args:
            content: LinkedIn profile content
            plan_data: Dictionary containing search parameters
            
        Returns:
            Boolean indicating whether to process this profile
        """
        # Import regex module for better pattern matching
        import re
        
        # Prepare content for matching (lowercase for case-insensitive matching)
        content_lower = content.lower()
        
        # Get role requirements from plan data
        target_roles = plan_data.get('target_roles', [])
        alternative_roles = plan_data.get('alternative_roles_titles', [])
        step_up_roles = plan_data.get('step_up_candidates', [])
        companies = plan_data.get('companies', [])
        
        # Check if no filtering criteria are provided
        if not target_roles and not alternative_roles and (not step_up_roles or step_up_roles == ['none']) and (not companies or companies == ['none']):
            # No filtering criteria, process all profiles
            logger.info("No filtering criteria provided, processing all profiles")
            return True
            
        # Check if any target role is mentioned in content
        role_match = False
        if target_roles:
            for role in target_roles:
                role_lower = role.lower()
                
                # Check for exact match with word boundaries
                if re.search(r'\b' + re.escape(role_lower) + r'\b', content_lower):
                    logger.info(f"Target role exact match found: {role}")
                    role_match = True
                    break
                    
                # Also check for reversed word order (e.g. "Director of Sales" vs "Sales Director")
                if ' ' in role_lower:
                    words = role_lower.split()
                    reversed_role = ' '.join(reversed(words))
                    if re.search(r'\b' + re.escape(reversed_role) + r'\b', content_lower):
                        logger.info(f"Target role reversed match found: {reversed_role} (original: {role})")
                        role_match = True
                        break
                        
                # For roles with 'of' in them, also check without the 'of'
                # E.g., "Director of Engineering" -> "Engineering Director"
                if ' of ' in role_lower:
                    parts = role_lower.split(' of ')
                    if len(parts) == 2:
                        alt_format = parts[1] + ' ' + parts[0]
                        if re.search(r'\b' + re.escape(alt_format) + r'\b', content_lower):
                            logger.info(f"Target role 'of' pattern match found: {alt_format} (original: {role})")
                            role_match = True
                            break
                    
        # If no target role match, check alternative roles
        if not role_match and alternative_roles:
            for role in alternative_roles:
                role_lower = role.lower()
                
                # Check for exact match with word boundaries
                if re.search(r'\b' + re.escape(role_lower) + r'\b', content_lower):
                    logger.info(f"Alternative role exact match found: {role}")
                    role_match = True
                    break
                    
                # Also check for reversed word order
                if ' ' in role_lower:
                    words = role_lower.split()
                    reversed_role = ' '.join(reversed(words))
                    if re.search(r'\b' + re.escape(reversed_role) + r'\b', content_lower):
                        logger.info(f"Alternative role reversed match found: {reversed_role} (original: {role})")
                        role_match = True
                        break
                        
                # For roles with 'of' in them, also check without the 'of'
                if ' of ' in role_lower:
                    parts = role_lower.split(' of ')
                    if len(parts) == 2:
                        alt_format = parts[1] + ' ' + parts[0]
                        if re.search(r'\b' + re.escape(alt_format) + r'\b', content_lower):
                            logger.info(f"Alternative role 'of' pattern match found: {alt_format} (original: {role})")
                            role_match = True
                            break
                    
        # If still no match, check step-up roles if provided and not set to 'none'
        if not role_match and step_up_roles and step_up_roles != ['none']:
            for role in step_up_roles:
                role_lower = role.lower()
                
                # Check for exact match with word boundaries
                if re.search(r'\b' + re.escape(role_lower) + r'\b', content_lower):
                    logger.info(f"Step-up role exact match found: {role}")
                    role_match = True
                    break
                    
                # Also check for reversed word order
                if ' ' in role_lower:
                    words = role_lower.split()
                    reversed_role = ' '.join(reversed(words))
                    if re.search(r'\b' + re.escape(reversed_role) + r'\b', content_lower):
                        logger.info(f"Step-up role reversed match found: {reversed_role} (original: {role})")
                        role_match = True
                        break
                        
                # For roles with 'of' in them, also check without the 'of'
                if ' of ' in role_lower:
                    parts = role_lower.split(' of ')
                    if len(parts) == 2:
                        alt_format = parts[1] + ' ' + parts[0]
                        if re.search(r'\b' + re.escape(alt_format) + r'\b', content_lower):
                            logger.info(f"Step-up role 'of' pattern match found: {alt_format} (original: {role})")
                            role_match = True
                            break
        
        # Check if any target company is mentioned in content
        company_match = False
        if companies and companies != ['none']:
            for company in companies:
                company_lower = company.lower()
                # Use word boundaries for company matching too
                if re.search(r'\b' + re.escape(company_lower) + r'\b', content_lower):
                    logger.info(f"Company match found: {company}")
                    company_match = True
                    break
        else:
            # If no companies specified, consider it a match
            company_match = True
            
        # Return true if both role and company criteria are met
        # If only role criteria is provided, only check roles
        # If only company criteria is provided, only check companies
        if companies and companies != ['none']:
            if target_roles or alternative_roles or (step_up_roles and step_up_roles != ['none']):
                # Both role and company criteria provided
                result = role_match and company_match
            else:
                # Only company criteria provided
                result = company_match
        else:
            # Only role criteria provided
            result = role_match
            
        if not result:
            logger.info("Profile doesn't match filtering criteria")
            # Log what was being checked
            criteria_log = []
            if target_roles:
                criteria_log.append(f"Target roles: {', '.join(target_roles)}")
            if alternative_roles:
                criteria_log.append(f"Alternative roles: {', '.join(alternative_roles)}")
            if step_up_roles and step_up_roles != ['none']:
                criteria_log.append(f"Step-up roles: {', '.join(step_up_roles)}")
            if companies and companies != ['none']:
                criteria_log.append(f"Companies: {', '.join(companies)}")
                
            logger.debug(f"Filtering criteria that weren't matched: {'; '.join(criteria_log)}")
            
        return result
    
    def _process_career_history(self, profile: CandidateProfile) -> None:
        """
        Parse career history text and create CareerHistory entries
        
        Args:
            profile: CandidateProfile with career history text to parse
        """
        try:
            career_history_text = profile.people_data.career_history
            if not career_history_text:
                logger.info("No career history text to parse")
                return
                
            logger.info("Parsing career history text")
            
            # Split career history text into individual positions
            # Format example: "Sep-2023 | Present: Client Account Manager at UBS, London\nApr-2022 | Sep-2023: Private Banking Executive at Barclays Corporate & Investment Bank, London"
            positions = career_history_text.split("\n")
            
            for position in positions:
                # Skip empty lines
                if not position.strip():
                    continue
                    
                # Parse the position string
                # First try to extract date range
                date_parts = None
                if " | " in position and ":" in position:
                    # Example: "Sep-2023 | Present: Client Account Manager at UBS, London"
                    date_section, role_section = position.split(":", 1)
                    if " | " in date_section:
                        start_date_raw, end_date_raw = date_section.split(" | ", 1)
                        
                        # Extract role and company
                        role_and_company = role_section.strip()
                        role = role_and_company
                        company_name = ""
                        
                        # Try to extract company name if "at" is present
                        if " at " in role_and_company:
                            role, company_section = role_and_company.split(" at ", 1)
                            role = role.strip()
                            
                            # Remove location if present (e.g., "Company, London")
                            if "," in company_section:
                                company_parts = company_section.split(",")
                                company_name = company_parts[0].strip()
                            else:
                                company_name = company_section.strip()
                        
                        # Parse dates
                        start_date = self._parse_career_date(start_date_raw.strip()) or None
                        end_date = None if end_date_raw.strip().lower() == "present" else self._parse_career_date(end_date_raw.strip())
                        
                        # Calculate tenure
                        tenure = 0.0
                        if start_date and (end_date or end_date_raw.strip().lower() == "present"):
                            from datetime import datetime
                            start_datetime = datetime.strptime(start_date, "%Y-%m-%d") if start_date else None
                            
                            if end_date_raw.strip().lower() == "present":
                                end_datetime = datetime.now()
                            else:
                                end_datetime = datetime.strptime(end_date, "%Y-%m-%d") if end_date else None
                                
                            if start_datetime and end_datetime:
                                # Calculate years as a float
                                diff = end_datetime - start_datetime
                                tenure = diff.days / 365.25
                        
                        # Create career history entry
                        history_entry = CareerHistory(
                            people_id=0,  # Will be set later when saving to DB
                            role=role,
                            company_name=company_name,
                            past_company_id=0,  # Will be set later when saving to DB
                            start_date=start_date,
                            end_date=end_date,
                            tenure=round(tenure, 1)
                        )
                        
                        profile.career_history.append(history_entry)
                        
            logger.info(f"Parsed {len(profile.career_history)} career history entries")
                
        except Exception as e:
            logger.error(f"Error parsing career history: {str(e)}")
    
    def _process_skills(self, profile: CandidateProfile) -> None:
        """
        Parse skills text and create Skill entries
        
        Args:
            profile: CandidateProfile with skills text to parse
        """
        try:
            skills_text = profile.people_data.skills
            if not skills_text:
                logger.info("No skills text to parse")
                return
                
            logger.info("Parsing skills text")
            
            # Split skills into individual entries
            skills = [skill.strip() for skill in skills_text.split(",") if skill.strip()]
            
            for skill_name in skills:
                # Determine skill type
                skill_type = self._determine_skill_type(skill_name)
                
                # Create skill entry
                skill_entry = Skill(
                    people_id=0,  # Will be set later when saving to DB
                    skill_name=skill_name,
                    skill_type=skill_type
                )
                
                profile.skills.append(skill_entry)
                
            logger.info(f"Parsed {len(profile.skills)} skills")
                
        except Exception as e:
            logger.error(f"Error parsing skills: {str(e)}")
    
    def _parse_career_date(self, date_string: str) -> Optional[str]:
        """
        Parse a date string from career history format (e.g. "Sep-2023")
        
        Args:
            date_string: The date string to parse
            
        Returns:
            String in YYYY-MM-DD format or None if parsing fails
        """
        try:
            # Handle 'Present' case
            if date_string.lower() == 'present':
                return datetime.now().strftime('%Y-%m-%d')
                
            # Try to parse "MMM-YYYY" format (e.g., "Sep-2023")
            from datetime import datetime
            import re
            
            # Match "MMM-YYYY" pattern
            match = re.match(r'^([A-Za-z]{3,})-(\d{4})$', date_string)
            if match:
                month_name, year = match.groups()
                month_abbr = month_name[:3].capitalize()
                
                # Convert month name to number
                try:
                    date_obj = datetime.strptime(f"{month_abbr} {year}", "%b %Y")
                    return date_obj.strftime("%Y-%m-%d")
                except ValueError:
                    pass
            
            # Try generic date parsing for other formats
            try:
                date_obj = datetime.strptime(date_string, "%Y-%m-%d")
                return date_string
            except ValueError:
                pass
                
            # Try more flexible parsing
            for fmt in ["%b %Y", "%B %Y", "%m/%Y", "%m-%Y", "%Y"]:
                try:
                    date_obj = datetime.strptime(date_string, fmt)
                    return date_obj.strftime("%Y-%m-%d")
                except ValueError:
                    continue
            
            # If all parsing attempts fail
            logger.warning(f"Could not parse date: {date_string}")
            return None
            
        except Exception as e:
            logger.error(f"Error parsing date {date_string}: {str(e)}")
            return None
    
    def _determine_skill_type(self, skill: str) -> str:
        """
        Determine the skill type based on keywords
        
        Args:
            skill: Skill name to categorize
            
        Returns:
            String representing skill type: Technical, Leadership, Communication, Financial, or Other
        """
        skill = skill.lower()
        
        # Technical skills
        technical_keywords = [
            'programming', 'coding', 'software', 'database', 'sql', 'python', 'java', 'c++',
            'javascript', 'react', 'angular', 'vue', 'node', 'aws', 'azure', 'cloud',
            'devops', 'infrastructure', 'security', 'network', 'system', 'architecture',
            'engineering', 'technical', 'development', 'automation', 'testing',
            'drilling', 'rig', 'offshore', 'onshore', 'oil', 'gas', 'petroleum', 'platform',
            'mechanical', 'electrical', 'operations', 'maintenance', 'safety', 'hsse'
        ]
        
        # Leadership skills
        leadership_keywords = [
            'leadership', 'management', 'strategic', 'executive', 'director', 'c-level',
            'board', 'governance', 'vision', 'strategy', 'planning', 'transformation',
            'change management', 'organizational', 'team lead', 'mentoring', 'coaching',
            'supervision', 'decision-making', 'delegation', 'influence'
        ]
        
        # Communication skills
        communication_keywords = [
            'communication', 'presentation', 'negotiation', 'public speaking', 'writing',
            'reporting', 'stakeholder', 'client', 'relationship', 'facilitation',
            'training', 'teaching', 'persuasion', 'influence', 'networking', 'collaboration'
        ]
        
        # Financial skills
        financial_keywords = [
            'financial', 'finance', 'accounting', 'budget', 'forecasting', 'investment',
            'revenue', 'profit', 'loss', 'balance sheet', 'p&l', 'tax', 'audit',
            'cost', 'pricing', 'valuation', 'funding', 'capital', 'banking', 'treasury'
        ]
        
        # Check skill against keyword lists
        for keyword in technical_keywords:
            if keyword in skill:
                return "Technical"
                
        for keyword in leadership_keywords:
            if keyword in skill:
                return "Leadership"
                
        for keyword in communication_keywords:
            if keyword in skill:
                return "Communication"
                
        for keyword in financial_keywords:
            if keyword in skill:
                return "Financial"
                
        # Default
        return "Other"
    
    def _create_profile_prompt(self, url: str, content: str, plan_data: Dict[str, Any]) -> str:
        """
        Create a prompt for the LLM to generate a candidate profile
        
        Args:
            url: LinkedIn profile URL
            content: LinkedIn profile content
            plan_data: Dictionary containing search parameters
            
        Returns:
            String containing the prompt
        """
        # Build role-specific instructions
        target_roles = plan_data.get('target_roles', [])
        role_instructions = "no specific target role"
        if target_roles:
            role_list = '", "'.join(target_roles)
            role_instructions = f'target roles: "{role_list}"'
            
        # Build step-up role instructions
        step_up_roles = plan_data.get('step_up_candidates', [])
        step_up_instructions = ""
        if step_up_roles and step_up_roles != ['none']:
            step_up_list = '", "'.join(step_up_roles)
            step_up_instructions = f'step-up roles: "{step_up_list}"'
        
        # Create the prompt
        prompt = f"""
Generate a candidate profile from this LinkedIn profile content. Create EXACTLY ONE profile.

LinkedIn Profile URL: {url}
Profile Content: {content}

Plan Requirements:
- Plan Name: {plan_data.get('plan_name', 'External Search')}
- Target Roles: {', '.join(target_roles)}
- Step-up Roles: {', '.join(step_up_roles) if step_up_roles and step_up_roles != ['none'] else 'None'}
- Companies: {', '.join(plan_data.get('companies', [])) if plan_data.get('companies', []) and plan_data.get('companies', []) != ['none'] else 'Any'}
- Gender Preference: {plan_data.get('gender', 'Not required')}
- Countries: {', '.join(plan_data.get('country', [])) if plan_data.get('country', []) and plan_data.get('country', []) != ['none'] else 'Any'}
- Minimum Tenure: {plan_data.get('minimum_tenure', 'None')}
- Required Skills: {', '.join(plan_data.get('skills', [])) if plan_data.get('skills', []) else 'None'}
- Required Qualifications: {', '.join(plan_data.get('qualifications', [])) if plan_data.get('qualifications', []) else 'None'}

CRITICAL INSTRUCTIONS:
1. Extract all available information from the LinkedIn profile
2. Format ALL fields EXACTLY according to the specified schema
3. For fields not explicitly available, make reasonable inferences based on available information
4. For required fields like company_name, if not found, use "Independent Professional" or "Not Specified"
5. Ensure ALL fields are present in the output, using null for optional fields when no information is available

CAREER HISTORY FORMAT:
For the career_history field, use this EXACT format for each entry:
"MMM-YYYY | MMM-YYYY: Job Title at Company Name, Location"
or "MMM-YYYY | Present: Job Title at Company Name, Location" for current role.

Example:
"Sep-2023 | Present: Client Account Manager at UBS, London
Apr-2022 | Sep-2023: Private Banking Executive at Barclays Corporate & Investment Bank, London
Jan-2020 | Apr-2022: Associate at Credit Suisse, Zurich"

SKILLS FORMAT:
For the skills field, use a comma-separated list of individual skills.
Example: "Project Management, Leadership, Financial Analysis, Strategic Planning, Team Building"

EDUCATIONAL HISTORY FORMAT:
Use the format: "Institution, Field of Study - Degree Type · Graduation Year"
Example: "University of Oxford, Business Administration - Master of Business Administration · 2015"

SCORING INSTRUCTIONS:
- role_match: CRITICAL - ASSIGN EXACTLY THESE VALUES ONLY:
  * 1.0 if and ONLY if the candidate's current role EXACTLY matches one of the target roles (case-insensitive, character-for-character)
  * 0.75 if and ONLY if the candidate's current role EXACTLY matches one of the step-up roles (case-insensitive, character-for-character)
  * 0.0 for ALL OTHER CASES - including similar roles, related roles, or partial matches
  * DO NOT use any other values (like 0.5) even for previous roles that match
- skills_match: Ratio of relevant skills for the target roles that the candidate possesses (between 0.0 and 1.0)
- location_match: 1.0 if in one of the specified countries, 0 otherwise. Default to 1.0 if no countries specified.
- gender_match: 1.0 if gender matches preference, 0 if mismatch. Default to 1.0 if no preference (Not required).
- tenure_match: 1.0 if tenure > minimum requirement, 0.5 if within 2 years below minimum, 0 if more than 2 years below minimum. Default to 1.0 if no minimum specified.
- education_match: Count of relevant qualifications matching the required qualifications. Default to 0 if no qualifications specified.
- total_score: Sum of role_match + skills_match + location_match + tenure_match + education_match

Provide detailed match_reasoning explaining how each score was calculated.
"""
        
        return prompt
    
    def _calculate_candidate_scores(self, profiles: List[CandidateProfile], plan_data: Dict[str, Any]) -> List[CandidateProfile]:
        """
        Calculate and verify scores for each candidate
        
        Args:
            profiles: List of CandidateProfile objects
            plan_data: Dictionary containing search parameters
            
        Returns:
            List of CandidateProfile objects with verified scores
        """
        for profile in profiles:
            self._verify_individual_scores(profile, plan_data)
            
        # Sort by total score in descending order
        profiles.sort(key=lambda p: p.pipeline_data.total_score, reverse=True)
        
        return profiles
    
    def _verify_individual_scores(self, profile: CandidateProfile, plan_data: Dict[str, Any]) -> None:
        """
        Verify and potentially update scores for an individual candidate
        
        Args:
            profile: CandidateProfile object
            plan_data: Dictionary containing search parameters
        """
        try:
            pipeline_data = profile.pipeline_data
            
            # Verify role match score
            candidate_role = pipeline_data.latest_role.lower() if pipeline_data.latest_role else ""
            
            calculated_role_match = 0.0
            
            # Check for exact match with target roles
            for role in plan_data.get('target_roles', []):
                if role.lower() == candidate_role:
                    calculated_role_match = 1.0
                    break
            
            # If not an exact match, check for step-up roles
            if calculated_role_match == 0.0 and plan_data.get('step_up_candidates', []) and plan_data.get('step_up_candidates', []) != ['none']:
                for role in plan_data.get('step_up_candidates', []):
                    if role.lower() == candidate_role:
                        calculated_role_match = 0.75
                        break
            
            # If AI assigned a much different score, log it and provide detailed explanation
            if abs(calculated_role_match - pipeline_data.role_match) > 0.25:
                logger.info(f"AI role match score differs from calculated: {pipeline_data.role_match} vs {calculated_role_match} for {candidate_role}", {
                    'candidate_role': candidate_role,
                    'target_roles': plan_data.get('target_roles', []),
                    'step_up_roles': plan_data.get('step_up_candidates', []),
                    'ai_score': pipeline_data.role_match,
                    'calculated_score': calculated_role_match,
                    'reason': 'Role scores should only be 1.0 for exact target role matches, 0.75 for exact step-up matches, or 0.0 otherwise'
                })
                
                # Override the AI's score with our calculated score to enforce consistency
                pipeline_data.role_match = calculated_role_match
                logger.info(f"Enforcing calculated role_match score: {calculated_role_match}")
            
            # Calculate total score
            total_score = (
                pipeline_data.role_match +
                pipeline_data.skills_match +
                pipeline_data.location_match +
                pipeline_data.tenure_match +
                pipeline_data.education_match
            )
            
            # Update total score if different
            if abs(total_score - pipeline_data.total_score) > 0.1:
                logger.info(f"Updating total score from {pipeline_data.total_score} to {total_score}", {
                    'original_score': pipeline_data.total_score,
                    'new_score': total_score,
                    'role_match': pipeline_data.role_match,
                    'skills_match': pipeline_data.skills_match,
                    'location_match': pipeline_data.location_match,
                    'tenure_match': pipeline_data.tenure_match,
                    'education_match': pipeline_data.education_match
                })
                pipeline_data.total_score = total_score
            
        except Exception as e:
            logger.error(f"Error verifying scores: {str(e)}")

def main():
    """Main function to run the script from the command line"""
    parser = argparse.ArgumentParser(description="Exa AI External Candidate Search")
    parser.add_argument("--query", help="Direct search query (e.g., 'Find CFO at Goldman Sachs')")
    parser.add_argument("--roles", nargs="+", help="Target roles to search for")
    parser.add_argument("--companies", nargs="+", help="Target companies to search for")
    parser.add_argument("--countries", nargs="+", help="Target countries to search for")
    parser.add_argument("--gender", choices=["Male", "Female", "Not required"], default="Not required", help="Gender preference")
    parser.add_argument("--min-tenure", type=int, help="Minimum tenure in years")
    parser.add_argument("--output", default="linkedin_profiles.json", help="Output file for results (default: linkedin_profiles.json)")
    parser.add_argument("--plan-file", help="JSON file containing plan data")
    parser.add_argument("--num-results", type=int, default=100, help="Number of search results to process (default: 100)")
    parser.add_argument("--batch-size", type=int, default=1, help="Number of profiles to process in a batch (experimental, default: 1)")
    parser.add_argument("--llm-filtering", action="store_true", default=True, help="Enable LLM-based preliminary filtering (default: enabled)")
    parser.add_argument("--no-llm-filtering", action="store_false", dest="llm_filtering", help="Disable LLM-based preliminary filtering")
    parser.add_argument("--debug", action="store_true", help="Enable additional debug logging")
    
    args = parser.parse_args()
    
    # Configure debug logging if requested
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.info("Debug logging enabled")
    
    # Initialize search engine
    try:
        search_engine = ExternalSearchEngine()
    except Exception as e:
        logger.critical(f"Failed to initialize search engine: {str(e)}")
        sys.exit(1)
    
    # Determine plan data source
    plan_data = {}
    
    if args.plan_file:
        # Load plan data from file
        try:
            logger.info(f"Loading plan data from file: {args.plan_file}")
            with open(args.plan_file, 'r') as f:
                plan_data = json.load(f)
            logger.info(f"Successfully loaded plan data for plan: {plan_data.get('plan_name', 'Unnamed')}")
        except Exception as e:
            logger.critical(f"Error loading plan file: {str(e)}")
            sys.exit(1)
    else:
        # Build plan data from command line arguments
        logger.info("Building plan data from command line arguments")
        plan_data = {
            'plan_name': 'Command Line Search',
            'plan_id': f'cli-{int(time.time())}',
            'target_roles': args.roles or [],
            'companies': args.companies or [],
            'country': args.countries or [],
            'gender': args.gender,
            'minimum_tenure': args.min_tenure,
            'search_query': args.query
        }
    
    # Add extra parameters
    plan_data['num_results'] = args.num_results
    plan_data['batch_size'] = args.batch_size
    plan_data['use_llm_filtering'] = args.llm_filtering
    
    # Log LLM filtering status
    logger.info(f"LLM filtering {'enabled' if args.llm_filtering else 'disabled'} for this run")
    
    # Ensure output path from command line takes precedence
    if args.output:
        logger.info(f"Using output file from command line: {args.output}")
        output_path = args.output
    else:
        # Default to a temporary file if no output specified
        output_path = f"search_results_{int(time.time())}.json"
        logger.info(f"No output file specified, using default: {output_path}")
    
    # Validate basic requirements - skip if search_query is provided
    if not plan_data.get('search_query') and not plan_data.get('target_roles'):
        logger.critical("Either search_query or target_roles is required")
        parser.print_help()
        sys.exit(1)
    
    # Ensure we have a plan_id
    if 'plan_id' not in plan_data:
        plan_data['plan_id'] = f'cli-{int(time.time())}'
        logger.warning(f"No plan_id found, using generated ID: {plan_data['plan_id']}")
    
    try:
        # Run the search
        logger.info(f"Starting search for plan: {plan_data.get('plan_name', 'Unnamed')}")
        results = search_engine.search_external_candidates(plan_data, output_path)
        
        # Display results
        if results:
            logger.info(f"Search completed successfully with {len(results)} candidates")
            print(f"\nFound {len(results)} candidates:")
            for i, result in enumerate(results, 1):
                print(f"\n{i}. {result['name']} - {result['role']} at {result['company']}")
                print(f"   LinkedIn: {result['url']}")
                print(f"   Score: {result['score']:.2f}")
        else:
            logger.warning("Search completed but no candidates were found")
            print("No candidates found.")
        
        if os.path.exists(args.output):
            file_size = os.path.getsize(args.output)
            print(f"\nFull results saved to {args.output} ({file_size} bytes)")
            logger.info(f"Full results saved to {args.output} ({file_size} bytes)")
        else:
            print(f"\nWARNING: Output file {args.output} was not created or cannot be accessed")
            logger.warning(f"Output file {args.output} does not exist after processing")
        
    except Exception as e:
        logger.critical(f"An error occurred during search: {str(e)}", exc_info=True)
        error_output = {
            "error": str(e),
            "traceback": traceback.format_exc(),
            "profiles": []
        }
        
        # Write error information to output file
        try:
            # Make sure the output path is absolute
            if not os.path.isabs(args.output):
                output_path = os.path.abspath(args.output)
            else:
                output_path = args.output
            
            # Make sure the directory exists
            output_dir = os.path.dirname(output_path)
            os.makedirs(output_dir, exist_ok=True)
            
            # Debug information about file access
            logger.info(f"Attempting to write error data to: {output_path}")
            logger.info(f"File directory exists: {os.path.exists(os.path.dirname(output_path))}")
            logger.info(f"File path is writable: {os.access(os.path.dirname(output_path), os.W_OK)}")
            
            # Write the error data
            with open(output_path, 'w') as f:
                json.dump(error_output, f, indent=2)
            
            # Check if file was actually created
            if os.path.exists(output_path):
                logger.info(f"Error information written to {output_path}")
                logger.info(f"File size: {os.path.getsize(output_path)} bytes")
            else:
                logger.error(f"Failed to save error data - file {output_path} does not exist after write")
        except Exception as write_error:
            logger.critical(f"Failed to write error information to output file: {str(write_error)}")
        
        # Exit with error code
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logger.critical(f"Unhandled exception in main: {str(e)}", exc_info=True)
        sys.exit(1)