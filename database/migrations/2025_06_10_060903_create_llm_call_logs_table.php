<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('llm_call_logs', function (Blueprint $table) {
            $table->id();
            $table->string('service', 50)->index(); // anthropic, openai, exa
            $table->string('endpoint')->index(); // messages, chat/completions, search
            $table->string('model')->nullable()->index(); // gpt-4o, claude-3-opus, etc.
            $table->longText('request_payload'); // Full request JSON
            $table->longText('response_payload'); // Full response JSON
            $table->decimal('duration_ms', 10, 2); // Request duration in milliseconds
            $table->json('tokens_used')->nullable(); // Token usage details
            $table->decimal('cost', 10, 6)->nullable()->index(); // Estimated cost
            $table->text('error')->nullable(); // Error message if failed
            $table->json('metadata')->nullable(); // Additional metadata
            $table->unsignedBigInteger('user_id')->nullable()->index();
            $table->string('session_id')->nullable()->index();
            $table->timestamps();
            
            // Indexes for common queries
            $table->index('created_at');
            $table->index(['service', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['service', 'model']);
            
            // Foreign key
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('llm_call_logs');
    }
};